package com.snct.hbase.utils;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

@HBaseTable(tableName = "test_bean")
public class TestBean {
    @HBaseColumn(family = "rowkey", qualifier = "id")
    private String id;
    
    @HBaseColumn(family = "info", qualifier = "name")
    private String name;
    
    @HBaseColumn(family = "info", qualifier = "age")
    private String age;
    
    public TestBean() {
        // 必须有无参构造函数
    }
    
    public TestBean(String id, String name, String age) {
        this.id = id;
        this.name = name;
        this.age = age;
    }
    
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getAge() {
        return age;
    }
    
    public void setAge(String age) {
        this.age = age;
    }
    
    @Override
    public String toString() {
        return "TestBean [id=" + id + ", name=" + name + ", age=" + age + "]";
    }
}