package com.snct.hbase.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snct.common.core.redis.RedisCache;
import com.snct.hbase.domain.hbase.GpsHbaseVo;
import com.snct.hbase.domain.hbase.AwsHbaseVo;
import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.kafka.KafkaMessage;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.util.HashSet;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * StoreService测试类
 * 测试save2Hbase方法的各种场景
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class StoreServiceTest {

    @Mock
    private RedisCache redisCache;

    @Mock
    private HBaseDaoUtil hBaseDaoUtil;

    @InjectMocks
    private StoreService storeService;

    private KafkaMessage gpsMessage;
    private KafkaMessage awsMessage;
    private KafkaMessage unsupportedMessage;

    @BeforeEach
    void setUp() {
        // 准备GPS测试数据
        gpsMessage = new KafkaMessage();
        gpsMessage.setType(StoreService.TYPE_GPS);
        gpsMessage.setCode("GPS001");
        gpsMessage.setSn("SHIP001");
        gpsMessage.setDeviceId(1001L);
        gpsMessage.setInitialTime(System.currentTimeMillis());
        
        // GPS消息内容
        JSONObject gpsData = new JSONObject();
        gpsData.put("shipId", 2001L);
        gpsData.put("deptId", 3001L);
        gpsData.put("shipName", "测试船只");
        gpsData.put("deptName", "测试部门");
        gpsData.put("deviceName", "GPS设备");
        gpsData.put("utcTime", "123456.00");
        gpsData.put("latitude", "39.9042");
        gpsData.put("longitude", "116.4074");
        gpsData.put("latitudeHemisphere", "N");
        gpsData.put("longitudeHemisphere", "E");
        gpsData.put("position", "1");
        gpsData.put("satellites", "8");
        gpsData.put("definition", "1.2");
        gpsData.put("antennaHeight", "10.5");
        gpsData.put("antennaUnit", "M");
        gpsData.put("geoidHeight", "5.2");
        gpsData.put("geoidUnit", "M");
        gpsMessage.setMsg(gpsData.toJSONString());

        // 准备AWS测试数据
        awsMessage = new KafkaMessage();
        awsMessage.setType(StoreService.TYPE_AWS);
        awsMessage.setCode("AWS001");
        awsMessage.setSn("SHIP002");
        awsMessage.setDeviceId(1002L);
        awsMessage.setInitialTime(System.currentTimeMillis());
        
        // AWS消息内容
        JSONObject awsData = new JSONObject();
        awsData.put("shipId", 2002L);
        awsData.put("deptId", 3002L);
        awsData.put("shipName", "测试船只2");
        awsData.put("deptName", "测试部门2");
        awsData.put("deviceName", "气象设备");
        awsData.put("latitude", "40.0042");
        awsData.put("longitude", "117.4074");
        awsData.put("relativeWind", "45.5");
        awsData.put("windLogoR", "R");
        awsData.put("relativeWindSpeed", "12.3");
        awsData.put("windSpeedUnit", "N");
        awsData.put("windStatus", "A");
        awsData.put("trueWind", "50.2");
        awsData.put("windLogoT", "T");
        awsData.put("trueWindSpeed", "15.8");
        awsData.put("airTemperature", "25.6");
        awsData.put("airUnit", "C");
        awsData.put("humidity", "65.2");
        awsData.put("humidityUnit", "P");
        awsData.put("pressure", "1013.25");
        awsMessage.setMsg(awsData.toJSONString());

        // 准备不支持的消息类型
        unsupportedMessage = new KafkaMessage();
        unsupportedMessage.setType(999);
        unsupportedMessage.setCode("UNKNOWN001");
        unsupportedMessage.setSn("SHIP999");
        unsupportedMessage.setDeviceId(9999L);
        unsupportedMessage.setInitialTime(System.currentTimeMillis());
        unsupportedMessage.setMsg("{}");
    }

    @Test
    void testSave2Hbase_GPS_Success() {
        // 模拟Redis缓存不存在
        when(redisCache.hasKey(anyString())).thenReturn(false);
        
        // 模拟HBase表不存在，需要创建
        when(hBaseDaoUtil.tableExists(anyString())).thenReturn(false);
        
        // 执行测试
        storeService.save2Hbase(gpsMessage);
        
        // 验证Redis缓存检查
        verify(redisCache).hasKey(contains("gps"));
        
        // 验证表存在性检查
        verify(hBaseDaoUtil).tableExists("snct:gps");
        
        // 验证表创建
        verify(hBaseDaoUtil).createTable(eq("snct:gps"), any(Set.class));
        
        // 验证数据保存
        verify(hBaseDaoUtil).save(eq("snct:gps"), any(GpsHbaseVo.class));
        
        // 验证Redis缓存设置
        verify(redisCache).setCacheObject(anyString(), anyLong(), eq(1L), eq(TimeUnit.DAYS));
    }

    @Test
    void testSave2Hbase_AWS_Success() {
        // 模拟Redis缓存不存在
        when(redisCache.hasKey(anyString())).thenReturn(false);
        
        // 模拟HBase表存在
        when(hBaseDaoUtil.tableExists(anyString())).thenReturn(true);
        
        // 执行测试
        storeService.save2Hbase(awsMessage);
        
        // 验证Redis缓存检查
        verify(redisCache).hasKey(contains("aws"));
        
        // 验证表存在性检查
        verify(hBaseDaoUtil).tableExists("snct:aws");
        
        // 验证不会创建表（因为表已存在）
        verify(hBaseDaoUtil, never()).createTable(anyString(), any(Set.class));
        
        // 验证数据保存
        verify(hBaseDaoUtil).save(eq("snct:aws"), any(AwsHbaseVo.class));
        
        // 验证Redis缓存设置
        verify(redisCache).setCacheObject(anyString(), anyLong(), eq(1L), eq(TimeUnit.DAYS));
    }

    @Test
    void testSave2Hbase_DataAlreadyProcessed() {
        // 模拟Redis缓存已存在（数据已处理）
        when(redisCache.hasKey(anyString())).thenReturn(true);
        
        // 执行测试
        storeService.save2Hbase(gpsMessage);
        
        // 验证Redis缓存检查
        verify(redisCache).hasKey(anyString());
        
        // 验证不会进行后续操作
        verify(hBaseDaoUtil, never()).tableExists(anyString());
        verify(hBaseDaoUtil, never()).createTable(anyString(), any(Set.class));
        verify(hBaseDaoUtil, never()).save(anyString(), any());
        verify(redisCache, never()).setCacheObject(anyString(), anyLong(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSave2Hbase_UnsupportedMessageType() {
        // 执行测试
        storeService.save2Hbase(unsupportedMessage);
        
        // 验证不会进行任何HBase或Redis操作
        verify(redisCache, never()).hasKey(anyString());
        verify(hBaseDaoUtil, never()).tableExists(anyString());
        verify(hBaseDaoUtil, never()).createTable(anyString(), any(Set.class));
        verify(hBaseDaoUtil, never()).save(anyString(), any());
        verify(redisCache, never()).setCacheObject(anyString(), anyLong(), anyLong(), any(TimeUnit.class));
    }

    @Test
    void testSave2Hbase_InitialTimeIsNull() {
        // 设置初始时间为null
        gpsMessage.setInitialTime(null);
        
        // 模拟Redis缓存不存在
        when(redisCache.hasKey(anyString())).thenReturn(false);
        when(hBaseDaoUtil.tableExists(anyString())).thenReturn(true);
        
        // 执行测试
        storeService.save2Hbase(gpsMessage);
        
        // 验证初始时间被设置为当前时间
        verify(redisCache).hasKey(anyString());
        verify(hBaseDaoUtil).save(anyString(), any(GpsHbaseVo.class));
        verify(redisCache).setCacheObject(anyString(), anyLong(), eq(1L), eq(TimeUnit.DAYS));
    }

    @Test
    void testSave2Hbase_ExceptionHandling() {
        // 模拟HBase操作抛出异常
        when(redisCache.hasKey(anyString())).thenReturn(false);
        when(hBaseDaoUtil.tableExists(anyString())).thenThrow(new RuntimeException("HBase连接失败"));
        
        // 执行测试（不应该抛出异常）
        storeService.save2Hbase(gpsMessage);
        
        // 验证异常被捕获，方法正常结束
        verify(redisCache).hasKey(anyString());
        verify(hBaseDaoUtil).tableExists(anyString());
    }

    @Test
    void testSave2Hbase_AllSupportedTypes() {
        // 测试所有支持的消息类型
        KafkaMessage[] messages = {
            createMessage(StoreService.TYPE_GPS, "GPS数据"),
            createMessage(StoreService.TYPE_AWS, "气象数据"),
            createMessage(StoreService.TYPE_ATTITUDE, "姿态数据"),
            createMessage(StoreService.TYPE_MODEM, "猫数据"),
            createMessage(StoreService.TYPE_AMPLIFIER, "功放数据"),
            createMessage(StoreService.TYPE_PDU, "PDU数据")
        };
        
        // 模拟Redis缓存不存在
        when(redisCache.hasKey(anyString())).thenReturn(false);
        when(hBaseDaoUtil.tableExists(anyString())).thenReturn(true);
        
        // 执行所有类型的测试
        for (KafkaMessage message : messages) {
            storeService.save2Hbase(message);
        }
        
        // 验证每种类型都被处理
        verify(redisCache, times(6)).hasKey(anyString());
    }

    /**
     * 创建测试消息的辅助方法
     */
    private KafkaMessage createMessage(int type, String description) {
        KafkaMessage message = new KafkaMessage();
        message.setType(type);
        message.setCode("TEST" + type);
        message.setSn("SHIP" + type);
        message.setDeviceId((long) type);
        message.setInitialTime(System.currentTimeMillis());
        
        JSONObject data = new JSONObject();
        data.put("description", description);
        data.put("shipId", 1000L + type);
        data.put("deptId", 2000L + type);
        data.put("shipName", "测试船只" + type);
        data.put("deptName", "测试部门" + type);
        data.put("deviceName", "测试设备" + type);
        message.setMsg(data.toJSONString());
        
        return message;
    }
}
