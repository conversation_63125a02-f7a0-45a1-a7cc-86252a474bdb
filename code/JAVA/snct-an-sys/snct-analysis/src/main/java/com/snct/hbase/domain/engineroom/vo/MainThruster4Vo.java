package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:main_thruster4")
public class MainThruster4Vo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 马达转速  05100
     */
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 马达扭矩  05101
     */
    @HBaseColumn(family = "i", qualifier = "torque")
    private String torque;

    /**
     * 马达功率 05102
     */
    @HBaseColumn(family = "i", qualifier = "power")
    private String power;

    /**
     * 马达电压  05103
     */
    @HBaseColumn(family = "i", qualifier = "voltage")
    private String voltage;

    /**
     * 马达电流  05104
     */
    @HBaseColumn(family = "i", qualifier = "current")
    private String current;

    /**
     * VFD IGBT TEMP   05105
     */
    @HBaseColumn(family = "i", qualifier = "vfdIgbt_t")
    private String vfdIgbtTemp;

    /**
     * 马达绕组U温度   05091
     */
    @HBaseColumn(family = "i", qualifier = "w_t_u")
    private String wTempU;

    /**
     * 马达绕组U温度   05092
     */
    @HBaseColumn(family = "i", qualifier = "w_t_v")
    private String wTempV;

    /**
     * 马达绕组U温度   05093
     */
    @HBaseColumn(family = "i", qualifier = "w_t_w")
    private String wTempW;

    /**
     * VFD 水冷单元水压  05108
     */
    @HBaseColumn(family = "i", qualifier = "vfdw_p")
    private String vfdwPress;

    /**
     * VFD 水冷单元水温  05109
     */
    @HBaseColumn(family = "i", qualifier = "vfdw_t")
    private String vfdwTemp;

    public MainThruster4Vo(){}

    public MainThruster4Vo(Map<String, EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get("05100"));
        this.torque = AnalysisUtils.analysis(map.get("05101"));
        this.power = AnalysisUtils.analysis(map.get("05102"));
        this.voltage = AnalysisUtils.analysis(map.get("05103"));
        this.current = AnalysisUtils.analysis(map.get("05104"));
        this.vfdIgbtTemp = AnalysisUtils.analysis(map.get("05105"));
        this.wTempU = AnalysisUtils.analysis(map.get("05091"));
        this.wTempV = AnalysisUtils.analysis(map.get("05092"));
        this.wTempW = AnalysisUtils.analysis(map.get("05093"));
        this.vfdwPress = AnalysisUtils.analysis(map.get("05108"));
        this.vfdIgbtTemp = AnalysisUtils.analysis(map.get("05109"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getTorque() {
        return torque;
    }

    public void setTorque(String torque) {
        this.torque = torque;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getVoltage() {
        return voltage;
    }

    public void setVoltage(String voltage) {
        this.voltage = voltage;
    }

    public String getCurrent() {
        return current;
    }

    public void setCurrent(String current) {
        this.current = current;
    }

    public String getVfdIgbtTemp() {
        return vfdIgbtTemp;
    }

    public void setVfdIgbtTemp(String vfdIgbtTemp) {
        this.vfdIgbtTemp = vfdIgbtTemp;
    }

    public String getwTempU() {
        return wTempU;
    }

    public void setwTempU(String wTempU) {
        this.wTempU = wTempU;
    }

    public String getwTempV() {
        return wTempV;
    }

    public void setwTempV(String wTempV) {
        this.wTempV = wTempV;
    }

    public String getwTempW() {
        return wTempW;
    }

    public void setwTempW(String wTempW) {
        this.wTempW = wTempW;
    }

    public String getVfdwPress() {
        return vfdwPress;
    }

    public void setVfdwPress(String vfdwPress) {
        this.vfdwPress = vfdwPress;
    }

    public String getVfdwTemp() {
        return vfdwTemp;
    }

    public void setVfdwTemp(String vfdwTemp) {
        this.vfdwTemp = vfdwTemp;
    }
}
