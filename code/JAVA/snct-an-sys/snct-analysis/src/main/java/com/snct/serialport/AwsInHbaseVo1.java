package com.snct.serialport;

import com.snct.common.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;

public class AwsInHbaseVo1 {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;

    @Excel(name = "录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    @Excel(name = "相对风向")
    @HBaseColumn(family = "i", qualifier = "r_w")
    private String relativeWind;

    @Excel(name = "相对风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_r")
    private String windLogoR;

    @Excel(name = "相对风速")
    @HBaseColumn(family = "i", qualifier = "r_w_s")
    private String relativeWindSpeed;

    @Excel(name = "真实风向")
    @HBaseColumn(family = "i", qualifier = "t_w")
    private String trueWind;

    @Excel(name = "真实风速")
    @HBaseColumn(family = "i", qualifier = "t_w_s")
    private String trueWindSpeed;

    @Excel(name = "真实风向标识")
    @HBaseColumn(family = "i", qualifier = "w_l_t")
    private String windLogoT;

    @Excel(name = "风速单位")
    @HBaseColumn(family = "i", qualifier = "wsu")
    private String windSpeedUnit;

    @Excel(name = "传感器类型（气温）")
    @HBaseColumn(family = "i", qualifier = "att")
    private String airTemType;

    @Excel(name = "气温值")
    @HBaseColumn(family = "i", qualifier = "a_t")
    private String airTemperature;

    @Excel(name = "气温值单位（°C）")
    @HBaseColumn(family = "i", qualifier = "au")
    private String airUnit;

    @Excel(name = "气温传感器ID")
    @HBaseColumn(family = "i", qualifier = "as")
    private String airSensor;

    @Excel(name = "传感器类型（相对湿度）")
    @HBaseColumn(family = "i", qualifier = "ht")
    private String humidityType;

    @Excel(name = "相对湿度数值")
    @HBaseColumn(family = "i", qualifier = "hum")
    private String humidity;

    @Excel(name = "标识相对湿度的单位")
    @HBaseColumn(family = "i", qualifier = "hu")
    private String humidityUnit;

    @Excel(name = "相对湿度传感器ID")
    @HBaseColumn(family = "i", qualifier = "hs")
    private String humiditySensor;

    @Excel(name = "传感器类型")
    @HBaseColumn(family = "i", qualifier = "ptt")
    private String pointTemType;

    @Excel(name = "露点温度数值")
    @HBaseColumn(family = "i", qualifier = "p_o")
    private String pointTem;

    @Excel(name = "露点温度传感器ID")
    @HBaseColumn(family = "i", qualifier = "pts")
    private String pointTemSensor;

    @Excel(name = "传感器类型")
    @HBaseColumn(family = "i", qualifier = "pt")
    private String pressureType;

    @Excel(name = "气压数值")
    @HBaseColumn(family = "i", qualifier = "p_r")
    private String pressure;

    @Excel(name = "气压传感器ID")
    @HBaseColumn(family = "i", qualifier = "ps")
    private String pressureSensor;

    @Excel(name = "气压单位正式")
    @HBaseColumn(family = "i", qualifier = "ps_u")
    private String pressureUnit;

    @Excel(name = "qfe气压type")
    @HBaseColumn(family = "i", qualifier = "qfe_t")
    private String qfeType;

    @Excel(name = "qfe气压数值")
    @HBaseColumn(family = "i", qualifier = "qfe")
    private String qfe;

    @Excel(name = "qfe气压单位")
    @HBaseColumn(family = "i", qualifier = "qfe_u")
    private String qfeUnit;

    @Excel(name = "qfe气压id")
    @HBaseColumn(family = "i", qualifier = "qfe_id")
    private String qfeId;

    @Excel(name = "qnh气压type")
    @HBaseColumn(family = "i", qualifier = "qnh_t")
    private String qnhType;

    @Excel(name = "qnh气压数值")
    @HBaseColumn(family = "i", qualifier = "qnh")
    private String qnh;

    @Excel(name = "qnh气压单位")
    @HBaseColumn(family = "i", qualifier = "qnh_u")
    private String qnhUnit;

    @Excel(name = "qnh气压id")
    @HBaseColumn(family = "i", qualifier = "qnh_id")
    private String qnhId;

    @Excel(name = "dp温度类型")
    @HBaseColumn(family = "i", qualifier = "dp_t")
    private String dpType;

    @Excel(name = "dp温度")
    @HBaseColumn(family = "i", qualifier = "dp")
    private String dp;

    @Excel(name = "dp温度单位")
    @HBaseColumn(family = "i", qualifier = "dp_u")
    private String dpUnit;

    @Excel(name = "dp温度id")
    @HBaseColumn(family = "i", qualifier = "dp_id")
    private String dpId;

    @Excel(name = "UTC时间")
    @HBaseColumn(family = "i", qualifier = "utc")
    private String utcTime;

    public String getId() {
        return this.id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getRelativeWind() {
        return this.relativeWind;
    }

    public void setRelativeWind(String relativeWind) {
        this.relativeWind = relativeWind;
    }

    public String getWindLogoR() {
        return this.windLogoR;
    }

    public void setWindLogoR(String windLogoR) {
        this.windLogoR = windLogoR;
    }

    public String getRelativeWindSpeed() {
        return this.relativeWindSpeed;
    }

    public void setRelativeWindSpeed(String relativeWindSpeed) {
        this.relativeWindSpeed = relativeWindSpeed;
    }

    public String getTrueWind() {
        return this.trueWind;
    }

    public void setTrueWind(String trueWind) {
        this.trueWind = trueWind;
    }

    public String getTrueWindSpeed() {
        return this.trueWindSpeed;
    }

    public void setTrueWindSpeed(String trueWindSpeed) {
        this.trueWindSpeed = trueWindSpeed;
    }

    public String getWindLogoT() {
        return this.windLogoT;
    }

    public void setWindLogoT(String windLogoT) {
        this.windLogoT = windLogoT;
    }

    public String getAirTemperature() {
        return this.airTemperature;
    }

    public void setAirTemperature(String airTemperature) {
        this.airTemperature = airTemperature;
    }

    public String getHumidity() {
        return this.humidity;
    }

    public void setHumidity(String humidity) {
        this.humidity = humidity;
    }

    public String getInitialTime() {
        return this.initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return this.initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getPointTem() {
        return this.pointTem;
    }

    public void setPointTem(String pointTem) {
        this.pointTem = pointTem;
    }

    public String getPressure() {
        return this.pressure;
    }

    public void setPressure(String pressure) {
        this.pressure = pressure;
    }

    public String getQfeType() {
        return this.qfeType;
    }

    public void setQfeType(String qfeType) {
        this.qfeType = qfeType;
    }

    public String getQfe() {
        return this.qfe;
    }

    public void setQfe(String qfe) {
        this.qfe = qfe;
    }

    public String getQfeUnit() {
        return this.qfeUnit;
    }

    public void setQfeUnit(String qfeUnit) {
        this.qfeUnit = qfeUnit;
    }

    public String getQfeId() {
        return this.qfeId;
    }

    public void setQfeId(String qfeId) {
        this.qfeId = qfeId;
    }

    public String getQnhType() {
        return this.qnhType;
    }

    public void setQnhType(String qnhType) {
        this.qnhType = qnhType;
    }

    public String getQnh() {
        return this.qnh;
    }

    public void setQnh(String qnh) {
        this.qnh = qnh;
    }

    public String getQnhUnit() {
        return this.qnhUnit;
    }

    public void setQnhUnit(String qnhUnit) {
        this.qnhUnit = qnhUnit;
    }

    public String getQnhId() {
        return this.qnhId;
    }

    public void setQnhId(String qnhId) {
        this.qnhId = qnhId;
    }

    public String getDpType() {
        return this.dpType;
    }

    public void setDpType(String dpType) {
        this.dpType = dpType;
    }

    public String getDp() {
        return this.dp;
    }

    public void setDp(String dp) {
        this.dp = dp;
    }

    public String getDpUnit() {
        return this.dpUnit;
    }

    public void setDpUnit(String dpUnit) {
        this.dpUnit = dpUnit;
    }

    public String getDpId() {
        return this.dpId;
    }

    public void setDpId(String dpId) {
        this.dpId = dpId;
    }

    public String getWindSpeedUnit() {
        return this.windSpeedUnit;
    }

    public void setWindSpeedUnit(String windSpeedUnit) {
        this.windSpeedUnit = windSpeedUnit;
    }

    public String getAirTemType() {
        return this.airTemType;
    }

    public void setAirTemType(String airTemType) {
        this.airTemType = airTemType;
    }

    public String getAirUnit() {
        return this.airUnit;
    }

    public void setAirUnit(String airUnit) {
        this.airUnit = airUnit;
    }

    public String getAirSensor() {
        return this.airSensor;
    }

    public void setAirSensor(String airSensor) {
        this.airSensor = airSensor;
    }

    public String getHumidityType() {
        return this.humidityType;
    }

    public void setHumidityType(String humidityType) {
        this.humidityType = humidityType;
    }

    public String getHumidityUnit() {
        return this.humidityUnit;
    }

    public void setHumidityUnit(String humidityUnit) {
        this.humidityUnit = humidityUnit;
    }

    public String getHumiditySensor() {
        return this.humiditySensor;
    }

    public void setHumiditySensor(String humiditySensor) {
        this.humiditySensor = humiditySensor;
    }

    public String getPointTemType() {
        return this.pointTemType;
    }

    public void setPointTemType(String pointTemType) {
        this.pointTemType = pointTemType;
    }

    public String getPointTemSensor() {
        return this.pointTemSensor;
    }

    public void setPointTemSensor(String pointTemSensor) {
        this.pointTemSensor = pointTemSensor;
    }

    public String getPressureType() {
        return this.pressureType;
    }

    public void setPressureType(String pressureType) {
        this.pressureType = pressureType;
    }

    public String getPressureSensor() {
        return this.pressureSensor;
    }

    public void setPressureSensor(String pressureSensor) {
        this.pressureSensor = pressureSensor;
    }

    public String getPressureUnit() {
        return this.pressureUnit;
    }

    public void setPressureUnit(String pressureUnit) {
        this.pressureUnit = pressureUnit;
    }

    public String getUtcTime() {
        return this.utcTime;
    }

    public void setUtcTime(String utcTime) {
        this.utcTime = utcTime;
    }
}