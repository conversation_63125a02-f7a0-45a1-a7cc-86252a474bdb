package com.snct.serialport;

import com.snct.common.annotation.Excel;

/**
 * @description: 姿态仪数据
 * <AUTHOR>
 * @date 2025-06-20
 **/
public class AttitudeHbaseVo1 {

    /**
     * 录入时间
     */
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    private String initialBjTime;

    /**
     * 横滚角
     */
    @Excel(name="横滚角")
    private String rolling;

    /**
     * 俯仰角
     */
    @Excel(name="俯仰角")
    private String pitch;

    /**
     * 航向角
     */
    @Excel(name="航向角")
    private String heading;

    /**
     * 经度
     */
    @Excel(name="经度")
    private String lon;

    /**
     * 纬度
     */
    @Excel(name="纬度")
    private String lat;

    /**
     * 高度
     */
    @Excel(name="高度")
    private String height;

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }

    public String getRolling() {
        return rolling;
    }

    public void setRolling(String rolling) {
        this.rolling = rolling;
    }

    public String getPitch() {
        return pitch;
    }

    public void setPitch(String pitch) {
        this.pitch = pitch;
    }

    public String getHeading() {
        return heading;
    }

    public void setHeading(String heading) {
        this.heading = heading;
    }

    public String getLon() {
        return lon;
    }

    public void setLon(String lon) {
        this.lon = lon;
    }

    public String getLat() {
        return lat;
    }

    public void setLat(String lat) {
        this.lat = lat;
    }

    public String getHeight() {
        return height;
    }

    public void setHeight(String height) {
        this.height = height;
    }
}
