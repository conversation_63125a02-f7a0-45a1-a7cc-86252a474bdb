package com.snct.hbase.domain.engineroom.vo;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * 侧推系统数据
 *
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:tunnel_thruster")
public class TunnelThrusterVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    String id;

    private Long time;

    private String bjTime;

    /**
     * 艏侧推(前)电机非驱动端轴承温度  07041
     */
    @Excel(name = "艏侧推电机非驱动端轴承温度")
    @HBaseColumn(family = "i", qualifier = "bt1mt")
    private String bt1MtrTemp;

    /**
     * 艏侧推马达绕组U 温度  07037
     */
    @Excel(name = "艏侧推马达绕组U 温度")
    @HBaseColumn(family = "i", qualifier = "bt1mwtu")
    private String bt1MtrWindingTempU;

    /**
     * 艏侧推马达绕组V 温度  07038
     */
    @Excel(name = "艏侧推马达绕组V 温度")
    @HBaseColumn(family = "i", qualifier = "bt1mwtt1")
    private String bt1MtrWindingTempV;

    /**
     * 艏侧推马达绕组W 温度  07039
     */
    @Excel(name = "艏侧推马达绕组W 温度")
    @HBaseColumn(family = "i", qualifier = "bt1mwtw")
    private String bt1MtrWindingTempW;

    /**
     * 艏侧推电机驱动端轴承温度  温度  07040
     */
    @Excel(name = "艏侧推电机驱动端轴承温度  温度")
    @HBaseColumn(family = "i", qualifier = "bt1mbt")
    private String bt1MtrBearTemp;

    /**
     * 艏侧推汇流排电压  07044
     */
    @Excel(name = "艏侧推汇流排电压")
    @HBaseColumn(family = "i", qualifier = "bt1bbv")
    private String bt1BusBarVoltage;

    /**
     * 艏侧推电机转速  07045
     */
    @Excel(name = "艏侧推电机转速")
    @HBaseColumn(family = "i", qualifier = "bt1ms")
    private String bt1MtrSpeed;

    /**
     * 艏侧推电机扭力  07046
     */
    @Excel(name = "艏侧推电机扭力")
    @HBaseColumn(family = "i", qualifier = "bt1mt")
    private String bt1MtrTorque;

    /**
     * 艏侧推电机功率  07047
     */
    @Excel(name = "艏侧推电机功率")
    @HBaseColumn(family = "i", qualifier = "bt1mp")
    private String bt1MtrPower;

    /**
     * 艏侧推电机电压  07048
     */
    @Excel(name = "艏侧推电机电压")
    @HBaseColumn(family = "i", qualifier = "bt1mv")
    private String bt1MtrVoltage;

    /**
     * 艏侧推电机电流  07049
     */
    @Excel(name = "艏侧推电机电流")
    @HBaseColumn(family = "i", qualifier = "bt1mc")
    private String bt1MtrCurrent;

    /**
     * 艏侧推变频器IGBT温度  07050
     */
    @Excel(name = "艏侧推变频器IGBT温度")
    @HBaseColumn(family = "i", qualifier = "bt1vit")
    private String bt1VfdIgbtTemp;

    /**
     * 艏侧推电机冷风温度  07042
     */
    @Excel(name = "艏侧推电机冷风温度")
    @HBaseColumn(family = "i", qualifier = "bt1mcwt")
    private String bt1MtrColdWindTemp;

    /**
     * 艏侧推电机热风温度  07043
     */
    @Excel(name = "艏侧推电机热风温度")
    @HBaseColumn(family = "i", qualifier = "bt1mhwt")
    private String bt1MtrHotWindTemp;

    /**
     * 艏侧推变频水冷单元水压  07053
     */
    @Excel(name = "艏侧推变频水冷单元水压")
    @HBaseColumn(family = "i", qualifier = "bt1vwp")
    private String bt1VfdWaterPress;

    /**
     * 艏侧推变频水冷单元水温  07054
     */
    @Excel(name = "艏侧推变频水冷单元水温")
    @HBaseColumn(family = "i", qualifier = "bt1vwt")
    private String bt1VfdWaterTemp;

    /**
     * 艏侧推(后)电机非驱动端轴承温度  09039
     */
    @Excel(name = "艏侧推(后)电机非驱动端轴承温度")
    @HBaseColumn(family = "i", qualifier = "st3mt")
    private String st3MtrTemp;

    /**
     * 艉侧推3 马达绕组U 温度  09035
     */
    @Excel(name = "艉侧推3 马达绕组U 温度")
    @HBaseColumn(family = "i", qualifier = "st3mwtu")
    private String st3MtrWindingTempU;

    /**
     * 艉侧推3 马达绕组V 温度  09036
     */
    @Excel(name = "艉侧推3 马达绕组V 温度")
    @HBaseColumn(family = "i", qualifier = "st3mwtv")
    private String st3MtrWindingTempV;

    /**
     * 艉侧推3 马达绕组W 温度  09037
     */
    @Excel(name = "艉侧推3 马达绕组W 温度")
    @HBaseColumn(family = "i", qualifier = "st3mwtw")
    private String st3MtrWindingTempW;

    /**
     * 艏侧推(后)电机驱动端轴承温度  温度  09038
     */
    @Excel(name = "艏侧推(后)电机驱动端轴承温度  温度")
    @HBaseColumn(family = "i", qualifier = "st3bmbt")
    private String st3MtrBearTemp;

    /**
     * 艏侧推（后）汇流排电压  09040
     */
    @Excel(name = "艏侧推（后）汇流排电压")
    @HBaseColumn(family = "i", qualifier = "st3bbv")
    private String st3BusBarVoltage;

    /**
     * 艏侧推（后)电机转速  09041
     */
    @Excel(name = "艏侧推(后）电机转速")
    @HBaseColumn(family = "i", qualifier = "st3ms")
    private String st3MtrSpeed;

    /**
     * 艏侧推（后）电机扭力  09042
     */
    @Excel(name = "艏侧推（后）电机扭力")
    @HBaseColumn(family = "i", qualifier = "st3mt")
    private String st3MtrTorque;

    /**
     * 艏侧推(后)电机功率  09043
     */
    @Excel(name = "艏侧推（后）电机功率")
    @HBaseColumn(family = "i", qualifier = "st3mp")
    private String st3MtrPower;

    /**
     * 艏侧推（后）电机电压  09044
     */
    @Excel(name = "艏侧推（后）电机电压")
    @HBaseColumn(family = "i", qualifier = "st3mv")
    private String st3MtrVoltage;

    /**
     * 艏侧推（后）电机电流  09045
     */
    @Excel(name = "艏侧推(后)电机电流")
    @HBaseColumn(family = "i", qualifier = "st3mc")
    private String st3MtrCurrent;

    /**
     * 艏侧推（后）变频器IGBT温度  09046
     */
    @Excel(name = "艏侧推（后)变频器IGBT温度")
    @HBaseColumn(family = "i", qualifier = "st3vit")
    private String st3VfdIgbtTemp;

    /**
     * 艏侧推（后）变频器水冷单元水压  09049
     */
    @Excel(name = "艏侧推（后)变频器水冷单元水压")
    @HBaseColumn(family = "i", qualifier = "st3vwp")
    private String st3VfdWaterPress;

    /**
     * 艏侧推（后）变频器水冷单元水温  09050
     */
    @Excel(name = "艏侧推（后)变频器水冷单元水温")
    @HBaseColumn(family = "i", qualifier = "st3vwt")
    private String st3VfdWaterTemp;

    /**
     * 艏侧推(中)电机非驱动端轴承温度  08039
     */
    @Excel(name = "艏侧推(中)电机非驱动端轴承温度")
    @HBaseColumn(family = "i", qualifier = "st2mt")
    private String st2MtrTemp;

    /**
     * 艉侧推2马达绕组U 温度  08035
     */
    @Excel(name = "艉侧推2 马达绕组U 温度")
    @HBaseColumn(family = "i", qualifier = "st2mwtu")
    private String st2MtrWindingTempU;

    /**
     * 艉侧推2 马达绕组V 温度  09036
     */
    @Excel(name = "艉侧推2 马达绕组V 温度")
    @HBaseColumn(family = "i", qualifier = "st2mwtv")
    private String st2MtrWindingTempV;

    /**
     * 艉侧推3 马达绕组W 温度  09037
     */
    @Excel(name = "艉侧推3 马达绕组W 温度")
    @HBaseColumn(family = "i", qualifier = "st2mwtw")
    private String st2MtrWindingTempW;

    /**
     * 艏侧推(中)电机驱动端轴承温度  温度  09038
     */
    @Excel(name = "艏侧推(中)电机驱动端轴承温度  温度")
    @HBaseColumn(family = "i", qualifier = "st2bmbt")
    private String st2MtrBearTemp;

    /**
     * 艏侧推（中）汇流排电压  08040
     */
    @Excel(name = "艏侧推（中）汇流排电压")
    @HBaseColumn(family = "i", qualifier = "st2bbv")
    private String st2BusBarVoltage;

    /**
     * 艏侧推（中)电机转速  08041
     */
    @Excel(name = "艏侧推(中）电机转速")
    @HBaseColumn(family = "i", qualifier = "st2ms")
    private String st2MtrSpeed;

    /**
     * 艏侧推（中）电机扭力  08042
     */
    @Excel(name = "艏侧推（中）电机扭力")
    @HBaseColumn(family = "i", qualifier = "st2mt")
    private String st2MtrTorque;

    /**
     * 艏侧推(中)电机功率  08043
     */
    @Excel(name = "艏侧推（中）电机功率")
    @HBaseColumn(family = "i", qualifier = "st2mp")
    private String st2MtrPower;

    /**
     * 艏侧推（中）电机电压  08044
     */
    @Excel(name = "艏侧推（中）电机电压")
    @HBaseColumn(family = "i", qualifier = "st2mv")
    private String st2MtrVoltage;

    /**
     * 艏侧推（中）电机电流  08045
     */
    @Excel(name = "艏侧推(中)电机电流")
    @HBaseColumn(family = "i", qualifier = "st2mc")
    private String st2MtrCurrent;

    /**
     * 艏侧推（中）变频器IGBT温度  08046
     */
    @Excel(name = "艏侧推（中)变频器IGBT温度")
    @HBaseColumn(family = "i", qualifier = "st2vit")
    private String st2VfdIgbtTemp;

    /**
     * 艏侧推（中）变频器水冷单元水压  08049
     */
    @Excel(name = "艏侧推（中)变频器水冷单元水压")
    @HBaseColumn(family = "i", qualifier = "st2vwp")
    private String st2VfdWaterPress;

    /**
     * 艏侧推（中）变频器水冷单元水温  08050
     */
    @Excel(name = "艏侧推（中)变频器水冷单元水温")
    @HBaseColumn(family = "i", qualifier = "st2vwt")
    private String st2VfdWaterTemp;

    public TunnelThrusterVo() {

    }

    public TunnelThrusterVo(Map<String, EngineroomData> map) {
        //前
        this.bt1MtrTemp = AnalysisUtils.analysis(map.get("07041"));
        this.bt1MtrWindingTempU = AnalysisUtils.analysis(map.get("07037"));
        this.bt1MtrWindingTempV = AnalysisUtils.analysis(map.get("07038"));
        this.bt1MtrWindingTempW = AnalysisUtils.analysis(map.get("07039"));
        this.bt1MtrBearTemp = AnalysisUtils.analysis(map.get("07040"));
        this.bt1BusBarVoltage = AnalysisUtils.analysis(map.get("07044"));
        this.bt1MtrSpeed = AnalysisUtils.analysis(map.get("07045"));
        this.bt1MtrTorque = AnalysisUtils.analysis(map.get("07046"));
        this.bt1MtrPower = AnalysisUtils.analysis(map.get("07047"));
        this.bt1MtrVoltage = AnalysisUtils.analysis(map.get("07048"));
        this.bt1MtrCurrent = AnalysisUtils.analysis(map.get("07049"));
        this.bt1VfdIgbtTemp = AnalysisUtils.analysis(map.get("07050"));
        this.bt1MtrColdWindTemp = AnalysisUtils.analysis(map.get("07042"));
        this.bt1MtrHotWindTemp = AnalysisUtils.analysis(map.get("07043"));
        this.bt1VfdWaterPress = AnalysisUtils.analysis(map.get("07053"));
        this.bt1VfdWaterTemp = AnalysisUtils.analysis(map.get("07054"));

        //中
        this.st2MtrTemp = AnalysisUtils.analysis(map.get("09039"));
        this.st2MtrWindingTempU = AnalysisUtils.analysis(map.get("09035"));
        this.st2MtrWindingTempV = AnalysisUtils.analysis(map.get("09036"));
        this.st2MtrWindingTempW = AnalysisUtils.analysis(map.get("09037"));
        this.st2MtrBearTemp = AnalysisUtils.analysis(map.get("09038"));
        this.st2BusBarVoltage = AnalysisUtils.analysis(map.get("08040"));
        this.st3MtrSpeed = AnalysisUtils.analysis(map.get("08041"));
        this.st2MtrTorque = AnalysisUtils.analysis(map.get("08042"));
        this.st2MtrPower = AnalysisUtils.analysis(map.get("08043"));
        this.st2MtrVoltage = AnalysisUtils.analysis(map.get("08044"));
        this.st2MtrCurrent = AnalysisUtils.analysis(map.get("08045"));
        this.st2VfdIgbtTemp = AnalysisUtils.analysis(map.get("08046"));
        this.st2VfdWaterPress = AnalysisUtils.analysis(map.get("08049"));
        this.st2VfdWaterTemp = AnalysisUtils.analysis(map.get("08050"));


        //后
        this.st3MtrTemp = AnalysisUtils.analysis(map.get("09039"));
        this.st3MtrWindingTempU = AnalysisUtils.analysis(map.get("09035"));
        this.st3MtrWindingTempV = AnalysisUtils.analysis(map.get("09036"));
        this.st3MtrWindingTempW = AnalysisUtils.analysis(map.get("09037"));
        this.st3MtrBearTemp = AnalysisUtils.analysis(map.get("09038"));
        this.st3BusBarVoltage = AnalysisUtils.analysis(map.get("09040"));
        this.st3MtrSpeed = AnalysisUtils.analysis(map.get("09041"));
        this.st3MtrTorque = AnalysisUtils.analysis(map.get("09042"));
        this.st3MtrPower = AnalysisUtils.analysis(map.get("09043"));
        this.st3MtrVoltage = AnalysisUtils.analysis(map.get("09044"));
        this.st3MtrCurrent = AnalysisUtils.analysis(map.get("09045"));
        this.st3VfdIgbtTemp = AnalysisUtils.analysis(map.get("09046"));
        this.st3VfdWaterPress = AnalysisUtils.analysis(map.get("09049"));
        this.st3VfdWaterTemp = AnalysisUtils.analysis(map.get("09050"));
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getBt1MtrTemp() {
        return bt1MtrTemp;
    }

    public void setBt1MtrTemp(String bt1MtrTemp) {
        this.bt1MtrTemp = bt1MtrTemp;
    }

    public String getBt1MtrWindingTempU() {
        return bt1MtrWindingTempU;
    }

    public void setBt1MtrWindingTempU(String bt1MtrWindingTempU) {
        this.bt1MtrWindingTempU = bt1MtrWindingTempU;
    }

    public String getBt1MtrWindingTempV() {
        return bt1MtrWindingTempV;
    }

    public void setBt1MtrWindingTempV(String bt1MtrWindingTempV) {
        this.bt1MtrWindingTempV = bt1MtrWindingTempV;
    }

    public String getBt1MtrWindingTempW() {
        return bt1MtrWindingTempW;
    }

    public void setBt1MtrWindingTempW(String bt1MtrWindingTempW) {
        this.bt1MtrWindingTempW = bt1MtrWindingTempW;
    }

    public String getBt1MtrBearTemp() {
        return bt1MtrBearTemp;
    }

    public void setBt1MtrBearTemp(String bt1MtrBearTemp) {
        this.bt1MtrBearTemp = bt1MtrBearTemp;
    }

    public String getBt1BusBarVoltage() {
        return bt1BusBarVoltage;
    }

    public void setBt1BusBarVoltage(String bt1BusBarVoltage) {
        this.bt1BusBarVoltage = bt1BusBarVoltage;
    }

    public String getBt1MtrSpeed() {
        return bt1MtrSpeed;
    }

    public void setBt1MtrSpeed(String bt1MtrSpeed) {
        this.bt1MtrSpeed = bt1MtrSpeed;
    }

    public String getBt1MtrTorque() {
        return bt1MtrTorque;
    }

    public void setBt1MtrTorque(String bt1MtrTorque) {
        this.bt1MtrTorque = bt1MtrTorque;
    }

    public String getBt1MtrPower() {
        return bt1MtrPower;
    }

    public void setBt1MtrPower(String bt1MtrPower) {
        this.bt1MtrPower = bt1MtrPower;
    }

    public String getBt1MtrVoltage() {
        return bt1MtrVoltage;
    }

    public void setBt1MtrVoltage(String bt1MtrVoltage) {
        this.bt1MtrVoltage = bt1MtrVoltage;
    }

    public String getBt1MtrCurrent() {
        return bt1MtrCurrent;
    }

    public void setBt1MtrCurrent(String bt1MtrCurrent) {
        this.bt1MtrCurrent = bt1MtrCurrent;
    }

    public String getBt1VfdIgbtTemp() {
        return bt1VfdIgbtTemp;
    }

    public void setBt1VfdIgbtTemp(String bt1VfdIgbtTemp) {
        this.bt1VfdIgbtTemp = bt1VfdIgbtTemp;
    }

    public String getBt1MtrColdWindTemp() {
        return bt1MtrColdWindTemp;
    }

    public void setBt1MtrColdWindTemp(String bt1MtrColdWindTemp) {
        this.bt1MtrColdWindTemp = bt1MtrColdWindTemp;
    }

    public String getBt1MtrHotWindTemp() {
        return bt1MtrHotWindTemp;
    }

    public void setBt1MtrHotWindTemp(String bt1MtrHotWindTemp) {
        this.bt1MtrHotWindTemp = bt1MtrHotWindTemp;
    }

    public String getBt1VfdWaterPress() {
        return bt1VfdWaterPress;
    }

    public void setBt1VfdWaterPress(String bt1VfdWaterPress) {
        this.bt1VfdWaterPress = bt1VfdWaterPress;
    }

    public String getBt1VfdWaterTemp() {
        return bt1VfdWaterTemp;
    }

    public void setBt1VfdWaterTemp(String bt1VfdWaterTemp) {
        this.bt1VfdWaterTemp = bt1VfdWaterTemp;
    }

    public String getSt3MtrTemp() {
        return st3MtrTemp;
    }

    public void setSt3MtrTemp(String st3MtrTemp) {
        this.st3MtrTemp = st3MtrTemp;
    }

    public String getSt3MtrWindingTempU() {
        return st3MtrWindingTempU;
    }

    public void setSt3MtrWindingTempU(String st3MtrWindingTempU) {
        this.st3MtrWindingTempU = st3MtrWindingTempU;
    }

    public String getSt3MtrWindingTempV() {
        return st3MtrWindingTempV;
    }

    public void setSt3MtrWindingTempV(String st3MtrWindingTempV) {
        this.st3MtrWindingTempV = st3MtrWindingTempV;
    }

    public String getSt3MtrWindingTempW() {
        return st3MtrWindingTempW;
    }

    public void setSt3MtrWindingTempW(String st3MtrWindingTempW) {
        this.st3MtrWindingTempW = st3MtrWindingTempW;
    }

    public String getSt3MtrBearTemp() {
        return st3MtrBearTemp;
    }

    public void setSt3MtrBearTemp(String st3MtrBearTemp) {
        this.st3MtrBearTemp = st3MtrBearTemp;
    }

    public String getSt3BusBarVoltage() {
        return st3BusBarVoltage;
    }

    public void setSt3BusBarVoltage(String st3BusBarVoltage) {
        this.st3BusBarVoltage = st3BusBarVoltage;
    }

    public String getSt3MtrSpeed() {
        return st3MtrSpeed;
    }

    public void setSt3MtrSpeed(String st3MtrSpeed) {
        this.st3MtrSpeed = st3MtrSpeed;
    }

    public String getSt3MtrTorque() {
        return st3MtrTorque;
    }

    public void setSt3MtrTorque(String st3MtrTorque) {
        this.st3MtrTorque = st3MtrTorque;
    }

    public String getSt3MtrPower() {
        return st3MtrPower;
    }

    public void setSt3MtrPower(String st3MtrPower) {
        this.st3MtrPower = st3MtrPower;
    }

    public String getSt3MtrVoltage() {
        return st3MtrVoltage;
    }

    public void setSt3MtrVoltage(String st3MtrVoltage) {
        this.st3MtrVoltage = st3MtrVoltage;
    }

    public String getSt3MtrCurrent() {
        return st3MtrCurrent;
    }

    public void setSt3MtrCurrent(String st3MtrCurrent) {
        this.st3MtrCurrent = st3MtrCurrent;
    }

    public String getSt3VfdIgbtTemp() {
        return st3VfdIgbtTemp;
    }

    public void setSt3VfdIgbtTemp(String st3VfdIgbtTemp) {
        this.st3VfdIgbtTemp = st3VfdIgbtTemp;
    }

    public String getSt3VfdWaterPress() {
        return st3VfdWaterPress;
    }

    public void setSt3VfdWaterPress(String st3VfdWaterPress) {
        this.st3VfdWaterPress = st3VfdWaterPress;
    }

    public String getSt3VfdWaterTemp() {
        return st3VfdWaterTemp;
    }

    public void setSt3VfdWaterTemp(String st3VfdWaterTemp) {
        this.st3VfdWaterTemp = st3VfdWaterTemp;
    }

    public String getSt2MtrTemp() {
        return st2MtrTemp;
    }

    public void setSt2MtrTemp(String st2MtrTemp) {
        this.st2MtrTemp = st2MtrTemp;
    }

    public String getSt2MtrWindingTempU() {
        return st2MtrWindingTempU;
    }

    public void setSt2MtrWindingTempU(String st2MtrWindingTempU) {
        this.st2MtrWindingTempU = st2MtrWindingTempU;
    }

    public String getSt2MtrWindingTempV() {
        return st2MtrWindingTempV;
    }

    public void setSt2MtrWindingTempV(String st2MtrWindingTempV) {
        this.st2MtrWindingTempV = st2MtrWindingTempV;
    }

    public String getSt2MtrWindingTempW() {
        return st2MtrWindingTempW;
    }

    public void setSt2MtrWindingTempW(String st2MtrWindingTempW) {
        this.st2MtrWindingTempW = st2MtrWindingTempW;
    }

    public String getSt2MtrBearTemp() {
        return st2MtrBearTemp;
    }

    public void setSt2MtrBearTemp(String st2MtrBearTemp) {
        this.st2MtrBearTemp = st2MtrBearTemp;
    }

    public String getSt2BusBarVoltage() {
        return st2BusBarVoltage;
    }

    public void setSt2BusBarVoltage(String st2BusBarVoltage) {
        this.st2BusBarVoltage = st2BusBarVoltage;
    }

    public String getSt2MtrSpeed() {
        return st2MtrSpeed;
    }

    public void setSt2MtrSpeed(String st2MtrSpeed) {
        this.st2MtrSpeed = st2MtrSpeed;
    }

    public String getSt2MtrTorque() {
        return st2MtrTorque;
    }

    public void setSt2MtrTorque(String st2MtrTorque) {
        this.st2MtrTorque = st2MtrTorque;
    }

    public String getSt2MtrPower() {
        return st2MtrPower;
    }

    public void setSt2MtrPower(String st2MtrPower) {
        this.st2MtrPower = st2MtrPower;
    }

    public String getSt2MtrVoltage() {
        return st2MtrVoltage;
    }

    public void setSt2MtrVoltage(String st2MtrVoltage) {
        this.st2MtrVoltage = st2MtrVoltage;
    }

    public String getSt2MtrCurrent() {
        return st2MtrCurrent;
    }

    public void setSt2MtrCurrent(String st2MtrCurrent) {
        this.st2MtrCurrent = st2MtrCurrent;
    }

    public String getSt2VfdIgbtTemp() {
        return st2VfdIgbtTemp;
    }

    public void setSt2VfdIgbtTemp(String st2VfdIgbtTemp) {
        this.st2VfdIgbtTemp = st2VfdIgbtTemp;
    }

    public String getSt2VfdWaterPress() {
        return st2VfdWaterPress;
    }

    public void setSt2VfdWaterPress(String st2VfdWaterPress) {
        this.st2VfdWaterPress = st2VfdWaterPress;
    }

    public String getSt2VfdWaterTemp() {
        return st2VfdWaterTemp;
    }

    public void setSt2VfdWaterTemp(String st2VfdWaterTemp) {
        this.st2VfdWaterTemp = st2VfdWaterTemp;
    }
}
