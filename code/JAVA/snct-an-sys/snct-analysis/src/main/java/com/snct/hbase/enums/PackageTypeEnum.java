package com.snct.hbase.enums;

/**
 * <br>
 * <b>功能描述:传感器数据的编码</b>
 *
 * <AUTHOR>
 */
public enum PackageTypeEnum implements IEnum {

    DEVICE_DATA(0, "传感器数据"),
    SNAPSHOT_DATA(1, "快照数据"),
    CONNECT_KEEP(2, "UDP连接维持"),
    DATA_SYNC(3, "数据同步"),
    DATA_SYNC_SUCCESS(4, "数据同步成功");

    private Integer value;
    private String alias;


    PackageTypeEnum(int value, String alias) {
        this.value = value;
        this.alias = alias;
    }

    public static PackageTypeEnum getEnumByValue(int value) {
        for (PackageTypeEnum deviceType : PackageTypeEnum.values()) {
            if (value == deviceType.value) {
                return deviceType;
            }
        }
        return null;
    }


    @Override
    public Integer getValue() {
        return value;
    }

    @Override
    public String getAlias() {
        return alias;
    }
}
