package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:ballast_water")
public class BallastWaterVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 1号压载水舱水位  14026
     */
    @HBaseColumn(family = "i", qualifier = "level")
    private String level;

    /**
     * 6号压载水舱（左）水位  14035
     */
    @HBaseColumn(family = "i", qualifier = "level_p6")
    private String levelP6;

    /**
     * 6号压载水舱（右）水位  14036
     */
    @HBaseColumn(family = "i", qualifier = "level_s6")
    private String levelS6;

    /**
     * 4号压载水舱（左）水位  14031
     */
    @HBaseColumn(family = "i", qualifier = "level_p4")
    private String levelP4;

    /**
     * 5号压载水舱（左）水位  14033
     */
    @HBaseColumn(family = "i", qualifier = "level_p5")
    private String levelP5;

    /**
     * 5号压载水舱（右）水位  14034
     */
    @HBaseColumn(family = "i", qualifier = "level_s5")
    private String levelS5;

    /**
     * 4号压载水舱（右）水位  14032
     */
    @HBaseColumn(family = "i", qualifier = "level_s4")
    private String levelS4;

    /**
     * 吃水（后）  14040
     */
    @HBaseColumn(family = "i", qualifier = "d_a")
    private String draftAfter;

    /**
     * 吃水（左）  14038
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String draftP;

    /**
     * 吃水（右）  14039
     */
    @HBaseColumn(family = "i", qualifier = "d_s")
    private String draftS;

    /**
     * 吃水（前）  14037
     */
    @HBaseColumn(family = "i", qualifier = "d_f")
    private String draftFore;

    /**
     * 3号压载水舱（左）水位  14029
     */
    @HBaseColumn(family = "i", qualifier = "level_p3")
    private String levelP3;

    /**
     * 2号压载水舱（左）水位  14027
     */
    @HBaseColumn(family = "i", qualifier = "level_p2")
    private String levelP2;

    /**
     * 3号压载水舱（右）水位  14030
     */
    @HBaseColumn(family = "i", qualifier = "level_s3")
    private String levelS3;

    /**
     * 2号压载水舱（右）水位  14028
     */
    @HBaseColumn(family = "i", qualifier = "level_s2")
    private String levelS2;

    public BallastWaterVo(){}

    public BallastWaterVo(Map<String, EngineroomData> map){

        this.level = AnalysisUtils.analysis(map.get("14026"));
        this.levelP6 = AnalysisUtils.analysis(map.get("14035"));
        this.levelS6 = AnalysisUtils.analysis(map.get("14036"));
        this.levelP4 = AnalysisUtils.analysis(map.get("14031"));
        this.levelP5 = AnalysisUtils.analysis(map.get("14033"));
        this.levelS5 = AnalysisUtils.analysis(map.get("14034"));
        this.levelS4 = AnalysisUtils.analysis(map.get("14032"));
        this.draftAfter = AnalysisUtils.analysis(map.get("14040"));
        this.draftP = AnalysisUtils.analysis(map.get("14038"));
        this.draftS = AnalysisUtils.analysis(map.get("14039"));
        this.draftFore = AnalysisUtils.analysis(map.get("14037"));
        this.levelP3 = AnalysisUtils.analysis(map.get("14029"));
        this.levelP2 = AnalysisUtils.analysis(map.get("14027"));
        this.levelS3 = AnalysisUtils.analysis(map.get("14030"));
        this.levelS2 = AnalysisUtils.analysis(map.get("14028"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getLevelP6() {
        return levelP6;
    }

    public void setLevelP6(String levelP6) {
        this.levelP6 = levelP6;
    }

    public String getLevelS6() {
        return levelS6;
    }

    public void setLevelS6(String levelS6) {
        this.levelS6 = levelS6;
    }

    public String getLevelP4() {
        return levelP4;
    }

    public void setLevelP4(String levelP4) {
        this.levelP4 = levelP4;
    }

    public String getLevelP5() {
        return levelP5;
    }

    public void setLevelP5(String levelP5) {
        this.levelP5 = levelP5;
    }

    public String getLevelS5() {
        return levelS5;
    }

    public void setLevelS5(String levelS5) {
        this.levelS5 = levelS5;
    }

    public String getLevelS4() {
        return levelS4;
    }

    public void setLevelS4(String levelS4) {
        this.levelS4 = levelS4;
    }

    public String getDraftAfter() {
        return draftAfter;
    }

    public void setDraftAfter(String draftAfter) {
        this.draftAfter = draftAfter;
    }

    public String getDraftP() {
        return draftP;
    }

    public void setDraftP(String draftP) {
        this.draftP = draftP;
    }

    public String getDraftS() {
        return draftS;
    }

    public void setDraftS(String draftS) {
        this.draftS = draftS;
    }

    public String getDraftFore() {
        return draftFore;
    }

    public void setDraftFore(String draftFore) {
        this.draftFore = draftFore;
    }

    public String getLevelP3() {
        return levelP3;
    }

    public void setLevelP3(String levelP3) {
        this.levelP3 = levelP3;
    }

    public String getLevelP2() {
        return levelP2;
    }

    public void setLevelP2(String levelP2) {
        this.levelP2 = levelP2;
    }

    public String getLevelS3() {
        return levelS3;
    }

    public void setLevelS3(String levelS3) {
        this.levelS3 = levelS3;
    }

    public String getLevelS2() {
        return levelS2;
    }

    public void setLevelS2(String levelS2) {
        this.levelS2 = levelS2;
    }
}
