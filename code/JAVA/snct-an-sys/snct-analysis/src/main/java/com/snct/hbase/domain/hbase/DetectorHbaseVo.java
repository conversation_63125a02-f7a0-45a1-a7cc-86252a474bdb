package com.snct.hbase.domain.hbase;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;

/**
 * @description:  探测仪数据
 * @author: rr
 * @create: 2020-06-03 10:34
 **/
@HBaseTable
public class DetectorHbaseVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 部门id
     */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private String deptId;

    /** sn */
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String sn;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "i_t")
    private String initialTime;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

//  ---------------------- SDDBT ---------------------------
    /**
     * 1.水深
     */
    @Excel(name="水深")
    @HBaseColumn(family = "i", qualifier = "d_f")
    private String depthF;
    /**
     * 2.单位 英尺
     */
    @Excel(name="单位 英尺")
    @HBaseColumn(family = "i", qualifier = "u_f")
    private String unitF;
    /**
     * 3.水深
     */
    @Excel(name="水深")
    @HBaseColumn(family = "i", qualifier = "d_m")
    private String depthM;
    /**
     * 4.单位 米
     */
    @Excel(name="单位 米")
    @HBaseColumn(family = "i", qualifier = "u_m")
    private String unitM;
//  ---------------------- SDDBT ---------------------------


//  ---------------------- SDDPT ---------------------------
    /**
     * 2.探测仪偏移量  有正【传感器到水管】负【传感器到龙骨】
     */
    @Excel(name="探测仪偏移量")
    @HBaseColumn(family = "i", qualifier = "offset")
    private String offset;
//  ---------------------- SDDPT ---------------------------

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public void setDeptId(String deptId)
    {
        this.deptId = deptId;
    }

    public String getDeptId()
    {
        return deptId;
    }

    public void setSn(String sn)
    {
        this.sn = sn;
    }

    public String getSn()
    {
        return sn;
    }

    public String getDepthM() {
        return depthM;
    }

    public void setDepthM(String depthM) {
        this.depthM = depthM;
    }

    public String getOffset() {
        return offset;
    }

    public void setOffset(String offset) {
        this.offset = offset;
    }

    public String getInitialTime() {
        return initialTime;
    }

    public void setInitialTime(String initialTime) {
        this.initialTime = initialTime;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
