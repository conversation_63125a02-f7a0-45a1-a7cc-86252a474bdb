package com.snct.kafka.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import com.snct.system.domain.Device;
import com.snct.system.domain.Ship;
import com.snct.system.service.IDeviceService;
import com.snct.system.service.IShipService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 设备同步数据处理器
 *
 * <AUTHOR>
 */
@Component
public class DeviceDataHandler extends AbstractDeviceHandler {

    @Autowired
    private IDeviceService deviceService;

    @Autowired 
    private IShipService shipService;

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Object> deviceInfo) {
        try {
            // 对于设备同步数据，jsonObject实际上是一个JSONArray的字符串表示
            JSONArray deviceArray = JSON.parseArray(jsonObject.toJSONString());
            if (deviceArray == null || deviceArray.isEmpty()) {
                logger.error("解析设备数据失败");
                return false;
            }

            // 通过SN查询船舶信息，获取deptId
            Long deptId = null;
            if (!StringUtils.isEmpty(sn)) {
                Ship queryShip = new Ship();
                queryShip.setSn(sn);
                List<Ship> ships = shipService.selectShipList(queryShip);
                if (ships != null && !ships.isEmpty()) {
                    Ship ship = ships.get(0);
                    deptId = ship.getDeptId();
                    logger.info("根据SN[{}]查询到船舶信息，部门ID为: {}", sn, deptId);
                } else {
                    logger.warn("未找到SN[{}]对应的船舶信息", sn);
                }
            } else {
                logger.warn("处理设备数据时未提供船舶SN，无法关联船舶信息");
            }

            // 处理设备列表
            List<Device> devices = deviceArray.toJavaList(Device.class);
            return processDevices(devices, deptId);
        } catch (Exception e) {
            logger.error("处理设备数据异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        // 设备同步数据通常不需要保存到HBase
        // 这里返回true表示处理成功
        logger.info("设备数据不需要保存到HBase");
        return true;
    }

    /**
     * 处理设备列表
     *
     * @param devices 设备列表
     * @param deptId  部门ID
     * @return 是否处理成功
     */
    private boolean processDevices(List<Device> devices, Long deptId) {
        int addCount = 0;
        int existCount = 0;
        boolean hasError = false;

        for (Device device : devices) {
            try {
                // 检查设备编码
                if (StringUtils.isEmpty(device.getCode())) {
                    logger.warn("设备数据缺少设备编码(code)标识");
                    continue;
                }

                // 设置设备的部门ID为船舶的部门ID
                if (deptId != null) {
                    device.setDeptId(deptId);
                }

                // 检查设备是否已存在
                Long daDeviceId = device.getId();
                Device queryDevice = new Device();
                queryDevice.setCode(device.getCode());
                List<Device> existingDevices = deviceService.selectDeviceList(queryDevice);
                if (existingDevices != null && !existingDevices.isEmpty()) {
                    existCount++;
                    logger.info("设备已存在，编码: {}, DA_DEVICE_ID: {}", device.getCode(), daDeviceId);
                    continue;
                }

                // 添加新设备
                device.setDaDeviceId(daDeviceId);
                int result = deviceService.insertDevice(device);
                if (result > 0) {
                    addCount++;
                    logger.info("成功添加新设备，名称: {}, 编码: {}, DA_DEVICE_ID: {}, DeptId: {}",
                            device.getName(), device.getCode(), daDeviceId, device.getDeptId());
                } else {
                    logger.error("添加设备失败，编码: {}", device.getCode());
                    hasError = true;
                }
            } catch (Exception e) {
                logger.error("处理设备出错, 编码: {}, 错误: {}", device.getCode(), e.getMessage(), e);
                hasError = true;
            }
        }

        logger.info("设备数据处理完成: 已存在 {} 个, 新增 {} 个", existCount, addCount);
        
        return addCount > 0 || (addCount == 0 && !hasError);
    }
} 