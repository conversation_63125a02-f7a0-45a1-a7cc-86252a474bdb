package com.snct.web.controller.system;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.snct.common.annotation.Log;
import com.snct.common.core.controller.BaseController;
import com.snct.common.core.domain.AjaxResult;
import com.snct.common.enums.BusinessType;
import com.snct.system.domain.SysDataAuth;
import com.snct.system.service.ISysDataAuthService;
import com.snct.common.utils.poi.ExcelUtil;
import com.snct.common.core.page.TableDataInfo;

/**
 * 数据权限Controller
 *
 * <AUTHOR>
 * @date 2025-04-08
 */
@RestController
@RequestMapping("/system/dataAuth")
public class SysDataAuthController extends BaseController
{
    @Autowired
    private ISysDataAuthService sysDataAuthService;

    /**
     * 查询数据权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:list')")
    @GetMapping("/list")
    public TableDataInfo list(SysDataAuth sysDataAuth)
    {
        startPage();
        List<SysDataAuth> list = sysDataAuthService.selectSysDataAuthList(sysDataAuth);
        return getDataTable(list);
    }

    /**
     * 导出数据权限列表
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:export')")
    @Log(title = "数据权限", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, SysDataAuth sysDataAuth)
    {
        List<SysDataAuth> list = sysDataAuthService.selectSysDataAuthList(sysDataAuth);
        ExcelUtil<SysDataAuth> util = new ExcelUtil<SysDataAuth>(SysDataAuth.class);
        util.exportExcel(response, list, "数据权限数据");
    }

    /**
     * 获取数据权限详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:query')")
    @GetMapping(value = "/{dataAuthId}")
    public AjaxResult getInfo(@PathVariable("dataAuthId") Long dataAuthId)
    {
        return success(sysDataAuthService.selectSysDataAuthByDataAuthId(dataAuthId));
    }

    /**
     * 新增数据权限
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:add')")
    @Log(title = "数据权限", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SysDataAuth sysDataAuth)
    {
        if (!sysDataAuthService.checkDataAuthNameUnique(sysDataAuth))
        {
            return error("新增数据权限'" + sysDataAuth.getDataAuthName() + "'失败，名称已存在");
        }
//        else if (!sysDataAuthService.checkDataAuthCodeUnique(sysDataAuth))
//        {
//            return error("新增岗位'" + sysDataAuth.getDataAuthCode() + "'失败，编码已存在");
//        }
        sysDataAuth.setCreateBy(getUsername());
        return toAjax(sysDataAuthService.insertSysDataAuth(sysDataAuth));
    }

    /**
     * 修改数据权限
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:edit')")
    @Log(title = "数据权限", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SysDataAuth sysDataAuth)
    {
        return toAjax(sysDataAuthService.updateSysDataAuth(sysDataAuth));
    }

    /**
     * 删除数据权限
     */
    @PreAuthorize("@ss.hasPermi('system:dataAuth:remove')")
    @Log(title = "数据权限", businessType = BusinessType.DELETE)
	@DeleteMapping("/{dataAuthIds}")
    public AjaxResult remove(@PathVariable Long[] dataAuthIds)
    {
        return toAjax(sysDataAuthService.deleteSysDataAuthByDataAuthIds(dataAuthIds));
    }
}
