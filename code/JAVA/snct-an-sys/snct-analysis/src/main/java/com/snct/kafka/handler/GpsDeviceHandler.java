package com.snct.kafka.handler;

import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 北斗设备数据处理器
 * 处理北斗2和北斗3设备数据并存储到HBase
 *
 * <AUTHOR>
 */
@Component
public class GpsDeviceHandler extends AbstractDeviceHandler {

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Object> deviceInfo) {
        // 暂时不需要保存到MySQL，直接返回成功
        return true;
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        try {
            storeService.save2Hbase(kafkaMessage);
            return true;
        } catch (Exception e) {
            logger.error("保存北斗设备数据到HBase时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
} 