package com.snct.duffy.utils;


import com.snct.common.utils.StringUtils;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.domain.hbase.HbaseColumnVo;
import org.apache.hadoop.hbase.Cell;
import org.apache.hadoop.hbase.client.Put;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.NavigableMap;

/**
 * @ClassName: HBaseBeanUtil
 * @Description: HBase 与 Java Bean 互转工具类
 * @author: wzewei
 * @date: 2025-08-12 09:27
 */
public class HBaseBeanUtil2 {

    private static final Logger logger = LoggerFactory.getLogger(HBaseBeanUtil2.class);

    /**
     * 将 JavaBean 转换为 HBase Put 对象
     *
     * @param obj JavaBean
     * @return Put
     */
    public static <T> Put beanToPut(T obj) throws Exception {
        Put put = new Put(Bytes.toBytes(parseObjId(obj)));
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();
        for (Field field : fields) {
            if (!field.isAnnotationPresent(HBaseColumn.class)) {
                continue;
            }
            field.setAccessible(true);
            HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
            String family = orm.family();
            String qualifier = orm.qualifier();
            if (StringUtils.isBlank(family) || StringUtils.isBlank(qualifier)) {
                continue;
            }
            if ("rowkey".equalsIgnoreCase(qualifier) || "rowkey".equalsIgnoreCase(family)) {
                continue;
            }
            Object value = field.get(obj);
            if (value == null || StringUtils.isBlank(value.toString())) {
                continue;
            }
            put.addColumn(Bytes.toBytes(family), Bytes.toBytes(qualifier), Bytes.toBytes(value.toString()));
        }
        return put;
    }

    /**
     * 从 Bean 中解析 rowkey
     */
    public static <T> String parseObjId(T obj) {
        Class<?> clazz = obj.getClass();
        try {
            Field field = clazz.getDeclaredField("id");
            field.setAccessible(true);
            Object object = field.get(obj);
            return object != null ? object.toString() : "";
        } catch (Exception e) {
            logger.error("解析 rowkey 失败", e);
        }
        return "";
    }

    /**
     * 将 HBase Result 转换为 JavaBean
     */
    public static <T> T resultToBean(Result result, T obj) throws Exception {
        if (result == null || result.isEmpty()) {
            return null;
        }
        Map<String, HbaseColumnVo> columnMap = getObjColumnMap(obj);
        return setValue2Bean(obj, columnMap, result);
    }

    /**
     * 批量转换 Result 列表为 JavaBean 列表
     */
    static <T> List<T> resultToBean(List<Result> results, T obj, List<T> objs) throws Exception {
        if (results == null || results.isEmpty()) {
            return objs;
        }
        Map<String, HbaseColumnVo> columnMap = getObjColumnMap(obj);
        for (Result result : results) {
            T bean = setValue2Bean(obj, columnMap, result);
            if (bean != null) {
                objs.add(bean);
            }
        }
        return objs;
    }

    /**
     * 获取对象属性与 HBase 列信息映射
     */
    private static <T> Map<String, HbaseColumnVo> getObjColumnMap(T obj) throws NoSuchMethodException, IllegalAccessException {
        Map<String, HbaseColumnVo> columnInfoMap = new HashMap<>();
        Class<?> clazz = obj.getClass();
        Field[] fields = clazz.getDeclaredFields();

        for (Field field : fields) {
            if (!field.isAnnotationPresent(HBaseColumn.class)) {
                continue;
            }
            HBaseColumn orm = field.getAnnotation(HBaseColumn.class);
            String family = orm.family();
            String qualifier = orm.qualifier();
            if (StringUtils.isBlank(family) || StringUtils.isBlank(qualifier)) {
                continue;
            }

            HbaseColumnVo hbaseColumnVo = new HbaseColumnVo();
            hbaseColumnVo.setFamily(family);
            hbaseColumnVo.setQualifier(qualifier);

            String fieldName = field.getName();
            String setMethodName = "set" + Character.toUpperCase(fieldName.charAt(0)) + fieldName.substring(1);
            Method setMethod = clazz.getMethod(setMethodName, field.getType());
            hbaseColumnVo.setSetMethod(setMethod);

            field.setAccessible(true);
            Object val = field.get(obj);
            if (val != null && StringUtils.isNotBlank(val.toString())) {
                hbaseColumnVo.setValue(val.toString());
            }

            columnInfoMap.put(qualifier, hbaseColumnVo);
        }
        return columnInfoMap;
    }

    /**
     * 从 Result 中获取值 Map（按列族 → 列限定符 → 值）
     */
    private static Map<String, String> getResultValueMap(Result result) {
        if (result.isEmpty()) {
            return null;
        }
        Map<String, String> valueMap = new HashMap<>();
        for (byte[] family : result.getMap().keySet()) {
            NavigableMap<byte[], byte[]> familyMap = result.getFamilyMap(family);
            for (Map.Entry<byte[], byte[]> entry : familyMap.entrySet()) {
                valueMap.put(Bytes.toString(entry.getKey()), Bytes.toString(entry.getValue()));
            }
        }
        return valueMap;
    }

    /**
     * 将 Result 中的值设置到 JavaBean 中
     */
    private static <T> T setValue2Bean(T obj, Map<String, HbaseColumnVo> columnMap, Result result)
            throws InvocationTargetException, IllegalAccessException {
        Map<String, String> valueMap = getResultValueMap(result);
        if (valueMap == null || valueMap.isEmpty()) {
            return null;
        }

        Class<?> clazz = obj.getClass();
        T beanClone;
        try {
            beanClone = (T) clazz.getDeclaredConstructor().newInstance();
        } catch (Exception e) {
            throw new RuntimeException("创建对象失败: " + clazz.getName(), e);
        }

        for (Map.Entry<String, HbaseColumnVo> cm : columnMap.entrySet()) {
            String valueStr = valueMap.get(cm.getKey());
            if (valueStr == null) {
                continue;
            }
            if (cm.getValue() == null) {
                continue;
            }
            Method setter = cm.getValue().getSetMethod();
            Class<?> paramType = setter.getParameterTypes()[0];
            Object convertedValue = convertValue(paramType, valueStr);
            setter.invoke(beanClone, convertedValue);
        }
        return beanClone;
    }

    /**
     * 将字符串转换为指定类型
     */
    private static Object convertValue(Class<?> targetType, String value) {
        if (targetType == String.class) {
            return value;
        } else if (targetType == Integer.class || targetType == int.class) {
            return Integer.valueOf(value);
        } else if (targetType == Long.class || targetType == long.class) {
            return Long.valueOf(value);
        } else if (targetType == Double.class || targetType == double.class) {
            return Double.valueOf(value);
        } else if (targetType == Float.class || targetType == float.class) {
            return Float.valueOf(value);
        } else if (targetType == Boolean.class || targetType == boolean.class) {
            return Boolean.valueOf(value);
        }
        return value;
    }

    /**
     * 根据列族和列限定符获取 Result 中的值
     */
    private static String getResultValueByType(Result result, String family, String qualifier, boolean timeStamp) {
        if (!timeStamp) {
            byte[] bytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes(qualifier));
            return bytes == null ? null : Bytes.toString(bytes);
        }
        List<Cell> cells = result.getColumnCells(Bytes.toBytes(family), Bytes.toBytes(qualifier));
        if (cells.size() == 1) {
            Cell cell = cells.get(0);
            return String.valueOf(cell.getTimestamp());
        }
        return "";
    }

}
