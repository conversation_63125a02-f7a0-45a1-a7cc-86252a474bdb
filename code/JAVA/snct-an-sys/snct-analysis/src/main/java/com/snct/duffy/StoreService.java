package com.snct.duffy;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.snct.duffy.domain.hbase.AmplifierHbaseVo;
import com.snct.duffy.domain.hbase.ModemHbaseVo;
import com.snct.duffy.domain.hbase.PduHbaseVo;
import com.snct.duffy.utils.HBaseDaoUtil2;
import com.snct.hbase.domain.hbase.AttitudeHbaseVo;
import com.snct.hbase.domain.hbase.AwsHbaseVo;
import com.snct.hbase.domain.hbase.GpsHbaseVo;
import com.snct.hbase.enums.DeviceTypeEnum;
import com.snct.hbase.utils.DateUtils;

import com.snct.kafka.KafkaMessage;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * @ClassName: StoreService
 * @Description: 存储服务
 * @author: wzewei
 * @date: 2025-08-12 09:16
 */
@Component
public class StoreService {

    public final static Logger logger = LoggerFactory.getLogger(StoreService.class);

    @Resource
    private ValueOperations<String, Object> valueOperations;
    @Autowired
    private HBaseDaoUtil2 HBaseDaoUtil2;

    /**
     * 抽稀的分钟集合
     */
    private static final Integer[] INTERVALS = {0, 1, 5, 15};

    /**
     * 保存到hbase中
     *
     * @param message
     */
    void save2Hbase(KafkaMessage message) {
        logger.info("Hbase需要保存数据--，{}", JSONObject.toJSONString(message));
        try {
            Class cl = getClassByType(message.getType());
            if (cl == null) {
                return;
            }
            Object hbaseVo = JSON.parseObject(message.getMsg(), cl);

            // 保存最全数据
            save2Hbase(message, hbaseVo, cl, 100);

            // 抽稀并保存
            //Object thinningVo = thinningData(message, hbaseVo);
            //Arrays.stream(INTERVALS).forEach(i -> save2Hbase(message, thinningVo, cl, i));
        } catch (Exception e) {
            logger.error("数据异常", e);
        }
    }

    /**
     * 保存到hbase中 判断数据是否需要抽稀，并保存
     *
     * @param message
     * @param hbaseVo
     * @param interval 时间间隔
     */
    private void save2Hbase(KafkaMessage message, Object hbaseVo, Class cl, Integer interval) {

        Long nTime = message.getInitialTime();
        // 把时间转换为往前最近的整n分钟
        if (interval != 0 && interval != 100) {
            nTime = DateUtils.fetchCompleteNMinute(message.getInitialTime(), interval, 0);
        }

        String rowKey = HBaseDaoUtil2.getRowKey(nTime);

        String typeStr = DeviceTypeEnum.getByValue(message.getType()).getAlias();

        String key = message.getCode() + "-VACUUMING-" + interval + "-" + rowKey;
        if (valueOperations.get(key) != null) {
            return;
        }

        setValue2Vo(hbaseVo, cl, "id", rowKey);
        setValue2Vo(hbaseVo, cl, "initialTime", String.valueOf(nTime));
        setValue2Vo(hbaseVo, cl, "initialBjTime", DateUtils.getDateToString(nTime));

        HBaseDaoUtil2.save(HBaseDaoUtil2.getTableName(message.getSn(), typeStr, message.getCode(), interval), hbaseVo);

        // 设置过期时间为1天
        valueOperations.set(key, nTime, 1, TimeUnit.DAYS);
    }

    /**
     * 通过配置抽稀需要传输的数据
     *
     * @param message
     * @param vo
     * @return
     * @throws IllegalAccessException
     * @throws NoSuchFieldException
     * @throws InstantiationException
     */
    private Object thinningData(KafkaMessage message, Object vo) throws IllegalAccessException, NoSuchFieldException,
            InstantiationException {
        //List<String> attributes = transferAttributeService.queryAttributesByCode(message.getCode());
        List<String> attributes = null;
        if (attributes == null || attributes.size() == 0) {
            return null;
        }

        Class cl = vo.getClass();
        Object thinningVo = cl.newInstance();

        for (String attr : attributes) {
            Field field = cl.getDeclaredField(attr);
            field.setAccessible(true);

            if (field.get(vo) != null) {
                setValue2Vo(thinningVo, cl, attr, field.get(vo).toString());
            }
        }

        return thinningVo;
    }

    /**
     * 给hbaseVo字段赋值
     *
     * @param hbaseVo
     * @param cl
     * @param fieldName
     * @param value
     */
    private void setValue2Vo(Object hbaseVo, Class cl, String fieldName, String value) {
        Field field;
        try {
            field = cl.getDeclaredField(fieldName);
            field.setAccessible(true);

            field.set(hbaseVo, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            logger.error("字段设置错误---{}", e);
        }
    }

    /**
     * 根据类型获取 类的Class
     *
     * @param type
     * @return
     */
    public Class getClassByType(Integer type) {
        switch (DeviceTypeEnum.getByValue(type)) {
            case GPS: {
                return GpsHbaseVo.class;
            }
            case AWS: {
                return AwsHbaseVo.class;
            }
            case ATTITUDE: {
                return AttitudeHbaseVo.class;
            }
            case PDU: {
                return PduHbaseVo.class;
            }
            case MODEM: {
                return ModemHbaseVo.class;
            }
            case AMPLIFIER: {
                return AmplifierHbaseVo.class;
            }
            default: {
                return null;
            }
        }
    }
}
