//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.Ship;
//import com.snct.system.service.IShipService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 船只Controller
// *
// * <AUTHOR>
// * @date 2025-04-08
// */
//@RestController
//@RequestMapping("/business/ship")
//public class ShipController extends BaseController
//{
//    @Autowired
//    private IShipService shipService;
//
//    /**
//     * 查询船只列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(Ship ship)
//    {
//        startPage();
//        List<Ship> list = shipService.selectShipList(ship);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出船只列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:export')")
//    @Log(title = "船只", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, Ship ship)
//    {
//        List<Ship> list = shipService.selectShipList(ship);
//        ExcelUtil<Ship> util = new ExcelUtil<Ship>(Ship.class);
//        util.exportExcel(response, list, "船只数据");
//    }
//
//    /**
//     * 获取船只详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:query')")
//    @GetMapping(value = "/{shipId}")
//    public AjaxResult getInfo(@PathVariable("shipId") Long shipId)
//    {
//        return success(shipService.selectShipByShipId(shipId));
//    }
//
//    /**
//     * 新增船只
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:add')")
//    @Log(title = "船只", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody Ship ship)
//    {
//        return toAjax(shipService.insertShip(ship));
//    }
//
//    /**
//     * 修改船只
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:edit')")
//    @Log(title = "船只", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody Ship ship)
//    {
//        return toAjax(shipService.updateShip(ship));
//    }
//
//    /**
//     * 删除船只
//     */
//    @PreAuthorize("@ss.hasPermi('business:ship:remove')")
//    @Log(title = "船只", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{shipIds}")
//    public AjaxResult remove(@PathVariable Long[] shipIds)
//    {
//        return toAjax(shipService.deleteShipByShipIds(shipIds));
//    }
//}
