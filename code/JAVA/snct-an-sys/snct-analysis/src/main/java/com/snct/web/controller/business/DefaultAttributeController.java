//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.DefaultAttribute;
//import com.snct.system.service.IDefaultAttributeService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 设备默认属性Controller
// *
// * <AUTHOR>
// * @date 2025-04-09
// */
//@RestController
//@RequestMapping("/business/defaultAttribute")
//public class DefaultAttributeController extends BaseController
//{
//    @Autowired
//    private IDefaultAttributeService defaultAttributeService;
//
//    /**
//     * 查询设备默认属性列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(DefaultAttribute defaultAttribute)
//    {
//        startPage();
//        List<DefaultAttribute> list = defaultAttributeService.selectDefaultAttributeList(defaultAttribute);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出设备默认属性列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:export')")
//    @Log(title = "设备默认属性", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, DefaultAttribute defaultAttribute)
//    {
//        List<DefaultAttribute> list = defaultAttributeService.selectDefaultAttributeList(defaultAttribute);
//        ExcelUtil<DefaultAttribute> util = new ExcelUtil<DefaultAttribute>(DefaultAttribute.class);
//        util.exportExcel(response, list, "设备默认属性数据");
//    }
//
//    /**
//     * 获取设备默认属性详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(defaultAttributeService.selectDefaultAttributeById(id));
//    }
//
//    /**
//     * 新增设备默认属性
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:add')")
//    @Log(title = "设备默认属性", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody DefaultAttribute defaultAttribute)
//    {
//        return toAjax(defaultAttributeService.insertDefaultAttribute(defaultAttribute));
//    }
//
//    /**
//     * 修改设备默认属性
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:edit')")
//    @Log(title = "设备默认属性", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody DefaultAttribute defaultAttribute)
//    {
//        return toAjax(defaultAttributeService.updateDefaultAttribute(defaultAttribute));
//    }
//
//    /**
//     * 删除设备默认属性
//     */
//    @PreAuthorize("@ss.hasPermi('business:defaultAttribute:remove')")
//    @Log(title = "设备默认属性", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(defaultAttributeService.deleteDefaultAttributeByIds(ids));
//    }
//}
