package com.snct.duffy.domain.hbase;


import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;

/**
 * @ClassName: AmplifierHbaseVo
 * @Description: 功放数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 14:50
 */
public class AmplifierHbaseVo {

    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;


    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;

    /** 衰减值 */
    @Excel(name = "衰减值")
    @HBaseColumn(family = "i", qualifier = "decay")
    private Double decay;

    /** 温度 */
    @Excel(name = "温度")
    @HBaseColumn(family = "i", qualifier = "temp")
    private Double temp;

    /** 输出功率 */
    @Excel(name = "输出功率")
    @HBaseColumn(family = "i", qualifier = "o_power")
    private Double outPower;

    /** 设备状态 */
    @Excel(name = "设备状态")
    @HBaseColumn(family = "i", qualifier = "status")
    private Long bucStatus;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setDecay(Double decay)
    {
        this.decay = decay;
    }

    public Double getDecay()
    {
        return decay;
    }

    public void setTemp(Double temp)
    {
        this.temp = temp;
    }

    public Double getTemp()
    {
        return temp;
    }

    public void setOutPower(Double outPower)
    {
        this.outPower = outPower;
    }

    public Double getOutPower()
    {
        return outPower;
    }

    public void setBucStatus(Long bucStatus)
    {
        this.bucStatus = bucStatus;
    }

    public Long getBucStatus()
    {
        return bucStatus;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}

