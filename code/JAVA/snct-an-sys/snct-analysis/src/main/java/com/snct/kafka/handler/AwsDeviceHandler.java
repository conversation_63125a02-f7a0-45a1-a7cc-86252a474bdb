package com.snct.kafka.handler;

import com.alibaba.fastjson.JSONObject;
import com.snct.kafka.KafkaMessage;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 气象仪设备数据处理器
 * 处理气象站数据并存储到MySQL和HBase
 *
 * <AUTHOR>
 */
@Component
public class AwsDeviceHandler extends AbstractDeviceHandler {

    @Override
    protected boolean saveToMysql(JSONObject jsonObject, String sn, Map<String, Object> deviceInfo) {
        // 暂时不需要保存到MySQL，直接返回成功
        return true;
    }

    @Override
    protected boolean saveToHbase(KafkaMessage kafkaMessage) {
        try {
            // 使用StoreService将气象仪数据保存到HBase
            storeService.save2Hbase(kafkaMessage);
            return true;
        } catch (Exception e) {
            logger.error("保存气象仪数据到HBase时发生异常: {}", e.getMessage(), e);
            return false;
        }
    }
} 