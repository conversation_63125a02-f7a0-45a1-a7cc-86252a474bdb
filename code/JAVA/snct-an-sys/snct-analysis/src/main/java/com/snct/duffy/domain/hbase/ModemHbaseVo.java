package com.snct.duffy.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;

/**
 * @ClassName: ModemHbaseVo
 * @Description: 卫星猫数据HBase存储实体类
 * @author: wzewei
 * @date: 2025-08-12 14:54
 */
public class ModemHbaseVo {
    /**
     * ID
     */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    /**
     * 录入时间
     */
    @HBaseColumn(family = "i", qualifier = "time")
    private String initialTime;

    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name = "录入时间")
    @HBaseColumn(family = "i", qualifier = "bj_time")
    private String initialBjTime;


    /**
     * 信号强度
     */
    @Excel(name = "信号强度")
    @HBaseColumn(family = "i", qualifier = "signal")
    private Double signal;

    /**
     * 速率
     */
    @Excel(name = "速率")
    @HBaseColumn(family = "i", qualifier = "speed")
    private Double speed;

    /**
     * 发送功率
     */
    @Excel(name = "发送功率")
    @HBaseColumn(family = "i", qualifier = "s_power")
    private Double sendPower;

    /**
     * 状态标志
     */
    @Excel(name = "状态标志")
    @HBaseColumn(family = "i", qualifier = "flag")
    private Long flag;

    public void setId(String id) {
        this.id = id;
    }

    public String getId() {
        return id;
    }

    public void setSignal(Double signal) {
        this.signal = signal;
    }

    public Double getSignal() {
        return signal;
    }

    public void setSpeed(Double speed) {
        this.speed = speed;
    }

    public Double getSpeed() {
        return speed;
    }

    public void setSendPower(Double sendPower) {
        this.sendPower = sendPower;
    }

    public Double getSendPower() {
        return sendPower;
    }

    public void setFlag(Long flag) {
        this.flag = flag;
    }

    public Long getFlag() {
        return flag;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
