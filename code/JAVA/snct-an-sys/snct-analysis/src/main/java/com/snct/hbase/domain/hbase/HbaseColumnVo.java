package com.snct.hbase.domain.hbase;

import java.lang.reflect.Method;

/**
 * class
 *
 * <AUTHOR>
 */
public class HbaseColumnVo {

    private String family;

    private String qualifier;

    private String value;

    private Method setMethod;

    public String getFamily() {
        return family;
    }

    public void setFamily(String family) {
        this.family = family;
    }

    public String getQualifier() {
        return qualifier;
    }

    public void setQualifier(String qualifier) {
        this.qualifier = qualifier;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public Method getSetMethod() {
        return setMethod;
    }

    public void setSetMethod(Method setMethod) {
        this.setMethod = setMethod;
    }
}
