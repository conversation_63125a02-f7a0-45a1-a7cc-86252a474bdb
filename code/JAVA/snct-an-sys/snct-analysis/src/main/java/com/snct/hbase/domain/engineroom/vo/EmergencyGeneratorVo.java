package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:emergency_generator")
public class EmergencyGeneratorVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 应急发动机转速  04001
     */
    @HBaseColumn(family = "i", qualifier = "speed")
    private String speed;

    /**
     * 应急发动机功率  19107.15
     */
    @HBaseColumn(family = "i", qualifier = "power")
    private String power;

    /**
     * 应急发动机功率因数  19107.16
     */
    @HBaseColumn(family = "i", qualifier = "power_f")
    private String powerFactor;

    /**
     * 应急发动机L1-L2相电压  19107.8
     */
    @HBaseColumn(family = "i", qualifier = "u_1_2")
    private String voltageL1L2;

    /**
     * 应急发动机L2-L3相电压  19107.9
     */
    @HBaseColumn(family = "i", qualifier = "u_2_3")
    private String voltageL2L3;

    /**
     * 应急发动机L3-L1相电压  19107.10
     */
    @HBaseColumn(family = "i", qualifier = "u_3_1")
    private String voltageL3L1;

    /**
     * 应急发动机L1相电流  19107.11
     */
    @HBaseColumn(family = "i", qualifier = "c_l_1")
    private String currentL1;

    /**
     * 应急发动机L2相电流  19107.12
     */
    @HBaseColumn(family = "i", qualifier = "c_l_2")
    private String currentL2;

    /**
     * 应急发动机L3相电流  19107.13
     */
    @HBaseColumn(family = "i", qualifier = "c_l_3")
    private String currentL3;

    /**
     * 应急发动机L1相频率  19107.14.1
     */
    @HBaseColumn(family = "i", qualifier = "f_l_1")
    private String frequencyL1;

    /**
     * 应急发动机L2相频率  19107.14.2
     */
    @HBaseColumn(family = "i", qualifier = "f_l_2")
    private String frequencyL2;

    /**
     * 应急发动机L3相频率  19107.14.3
     */
    @HBaseColumn(family = "i", qualifier = "f_l_3")
    private String frequencyL3;

    /**
     * 应急发动机定子绕组U温度  04020
     */
    @HBaseColumn(family = "i", qualifier = "w_t_u")
    private String tempU;

    /**
     * 应急发动机定子绕组U温度  04021
     */
    @HBaseColumn(family = "i", qualifier = "w_t_v")
    private String tempV;

    /**
     * 应急发动机定子绕组U温度  04022
     */
    @HBaseColumn(family = "i", qualifier = "w_t_w")
    private String tempW;

    public EmergencyGeneratorVo(){}

    public EmergencyGeneratorVo(Map<String, EngineroomData> map){

        this.speed = AnalysisUtils.analysis(map.get("04001"));
        this.power = AnalysisUtils.analysis(map.get("19107.15"));
        this.powerFactor = AnalysisUtils.analysis(map.get("19107.16"));
        this.voltageL1L2 = AnalysisUtils.analysis(map.get("19107.8"));
        this.voltageL2L3 = AnalysisUtils.analysis(map.get("19107.9"));
        this.voltageL3L1 = AnalysisUtils.analysis(map.get("19107.10"));
        this.currentL1 = AnalysisUtils.analysis(map.get("19107.11"));
        this.currentL2 = AnalysisUtils.analysis(map.get("19107.12"));
        this.currentL3 = AnalysisUtils.analysis(map.get("19107.13"));
        this.frequencyL1 = AnalysisUtils.analysis(map.get("19107.14.1"));
        this.frequencyL2 = AnalysisUtils.analysis(map.get("19107.14.2"));
        this.frequencyL3 = AnalysisUtils.analysis(map.get("19107.14.3"));
        this.tempU = AnalysisUtils.analysis(map.get("04020"));
        this.tempV = AnalysisUtils.analysis(map.get("04021"));
        this.tempW = AnalysisUtils.analysis(map.get("04022"));

    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getSpeed() {
        return speed;
    }

    public void setSpeed(String speed) {
        this.speed = speed;
    }

    public String getPower() {
        return power;
    }

    public void setPower(String power) {
        this.power = power;
    }

    public String getPowerFactor() {
        return powerFactor;
    }

    public void setPowerFactor(String powerFactor) {
        this.powerFactor = powerFactor;
    }

    public String getVoltageL1L2() {
        return voltageL1L2;
    }

    public void setVoltageL1L2(String voltageL1L2) {
        this.voltageL1L2 = voltageL1L2;
    }

    public String getVoltageL2L3() {
        return voltageL2L3;
    }

    public void setVoltageL2L3(String voltageL2L3) {
        this.voltageL2L3 = voltageL2L3;
    }

    public String getVoltageL3L1() {
        return voltageL3L1;
    }

    public void setVoltageL3L1(String voltageL3L1) {
        this.voltageL3L1 = voltageL3L1;
    }

    public String getCurrentL1() {
        return currentL1;
    }

    public void setCurrentL1(String currentL1) {
        this.currentL1 = currentL1;
    }

    public String getCurrentL2() {
        return currentL2;
    }

    public void setCurrentL2(String currentL2) {
        this.currentL2 = currentL2;
    }

    public String getCurrentL3() {
        return currentL3;
    }

    public void setCurrentL3(String currentL3) {
        this.currentL3 = currentL3;
    }

    public String getFrequencyL1() {
        return frequencyL1;
    }

    public void setFrequencyL1(String frequencyL1) {
        this.frequencyL1 = frequencyL1;
    }

    public String getFrequencyL2() {
        return frequencyL2;
    }

    public void setFrequencyL2(String frequencyL2) {
        this.frequencyL2 = frequencyL2;
    }

    public String getFrequencyL3() {
        return frequencyL3;
    }

    public void setFrequencyL3(String frequencyL3) {
        this.frequencyL3 = frequencyL3;
    }

    public String getTempU() {
        return tempU;
    }

    public void setTempU(String tempU) {
        this.tempU = tempU;
    }

    public String getTempV() {
        return tempV;
    }

    public void setTempV(String tempV) {
        this.tempV = tempV;
    }

    public String getTempW() {
        return tempW;
    }

    public void setTempW(String tempW) {
        this.tempW = tempW;
    }
}
