package com.snct.hbase.domain.engineroom.vo;

import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;
import com.snct.hbase.domain.engineroom.EngineroomData;
import com.snct.hbase.utils.AnalysisUtils;

import java.util.Map;

/**
 * <AUTHOR>
 */
@HBaseTable(tableName = "ns1:compressed_air")
public class CompressedAirVo {

    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;

    private Long time;

    private String bjTime;

    /**
     * 控制空气瓶压力  16015
     */
    @HBaseColumn(family = "i", qualifier = "ca_p")
    private String caPress;

    /**
     * 1号主空气瓶压力  16012
     */
    @HBaseColumn(family = "i", qualifier = "ma_p1")
    private String maPress1;

    /**
     * 2号主空气瓶压力  16013
     */
    @HBaseColumn(family = "i", qualifier = "ma_p2")
    private String maPress2;

    /**
     * 辅空气瓶压力  16014
     */
    @HBaseColumn(family = "i", qualifier = "aa_p")
    private String aaPress;

    /**
     * 1#发电机启动空气压力  01013
     */
    @HBaseColumn(family = "i", qualifier = "ap1")
    private String airPressure1;

    /**
     * 2#发电机启动空气压力  02013
     */
    @HBaseColumn(family = "i", qualifier = "ap2")
    private String airPressure2;

    /**
     * 3#发电机启动空气压力  03013
     */
    @HBaseColumn(family = "i", qualifier = "ap3")
    private String airPressure3;

    public CompressedAirVo(){}

    public CompressedAirVo(Map<String, EngineroomData> map){

        this.caPress = AnalysisUtils.analysis(map.get("16015"));
        this.maPress1 = AnalysisUtils.analysis(map.get("16012"));
        this.maPress2 = AnalysisUtils.analysis(map.get("16013"));
        this.aaPress = AnalysisUtils.analysis(map.get("16014"));
    }


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public Long getTime() {
        return time;
    }

    public void setTime(Long time) {
        this.time = time;
    }

    public String getBjTime() {
        return bjTime;
    }

    public void setBjTime(String bjTime) {
        this.bjTime = bjTime;
    }

    public String getCaPress() {
        return caPress;
    }

    public void setCaPress(String caPress) {
        this.caPress = caPress;
    }

    public String getMaPress1() {
        return maPress1;
    }

    public void setMaPress1(String maPress1) {
        this.maPress1 = maPress1;
    }

    public String getMaPress2() {
        return maPress2;
    }

    public void setMaPress2(String maPress2) {
        this.maPress2 = maPress2;
    }

    public String getAaPress() {
        return aaPress;
    }

    public void setAaPress(String aaPress) {
        this.aaPress = aaPress;
    }

    public String getAirPressure1() {
        return airPressure1;
    }

    public void setAirPressure1(String airPressure1) {
        this.airPressure1 = airPressure1;
    }

    public String getAirPressure2() {
        return airPressure2;
    }

    public void setAirPressure2(String airPressure2) {
        this.airPressure2 = airPressure2;
    }

    public String getAirPressure3() {
        return airPressure3;
    }

    public void setAirPressure3(String airPressure3) {
        this.airPressure3 = airPressure3;
    }
}
