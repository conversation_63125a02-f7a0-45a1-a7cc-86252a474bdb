//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.SerialConfig;
//import com.snct.system.service.ISerialConfigService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * 串口配置Controller
// *
// * <AUTHOR>
// * @date 2025-04-09
// */
//@RestController
//@RequestMapping("/business/serialConfig")
//public class SerialConfigController extends BaseController
//{
//    @Autowired
//    private ISerialConfigService serialConfigService;
//
//    /**
//     * 查询串口配置列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(SerialConfig serialConfig)
//    {
//        startPage();
//        List<SerialConfig> list = serialConfigService.selectSerialConfigList(serialConfig);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出串口配置列表
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:export')")
//    @Log(title = "串口配置", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, SerialConfig serialConfig)
//    {
//        List<SerialConfig> list = serialConfigService.selectSerialConfigList(serialConfig);
//        ExcelUtil<SerialConfig> util = new ExcelUtil<SerialConfig>(SerialConfig.class);
//        util.exportExcel(response, list, "串口配置数据");
//    }
//
//    /**
//     * 获取串口配置详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(serialConfigService.selectSerialConfigById(id));
//    }
//
//    /**
//     * 新增串口配置
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:add')")
//    @Log(title = "串口配置", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody SerialConfig serialConfig)
//    {
//        return toAjax(serialConfigService.insertSerialConfig(serialConfig));
//    }
//
//    /**
//     * 修改串口配置
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:edit')")
//    @Log(title = "串口配置", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody SerialConfig serialConfig)
//    {
//        return toAjax(serialConfigService.updateSerialConfig(serialConfig));
//    }
//
//    /**
//     * 删除串口配置
//     */
//    @PreAuthorize("@ss.hasPermi('business:serialConfig:remove')")
//    @Log(title = "串口配置", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(serialConfigService.deleteSerialConfigByIds(ids));
//    }
//}
