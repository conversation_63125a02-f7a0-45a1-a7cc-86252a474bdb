package com.snct.hbase.exception;

/**
 * class
 *
 * <AUTHOR>
 * @date 2020/3/25 0:57
 */
public class JobException extends Exception {
    private Integer errorCode;

    public JobException(){}

    public JobException(String message){
        super(message);
    }

    public JobException(String message, Throwable cause){
        super(message,cause);
    }

    public JobException(Integer errorCode, String message, Throwable cause){
        super(message,cause);
        this.errorCode = errorCode;
    }

    public JobException(Throwable cause) {
        super(cause);
    }

    public Integer getErrorCode(){return errorCode;}

    public void setErrorCode(Integer errorCode) {
        this.errorCode = errorCode;
    }
}
