package com.snct.hbase.service;

import com.snct.hbase.utils.HBaseDaoUtil;
import com.snct.hbase.domain.hbase.PduHbaseVo;
import com.snct.hbase.utils.HConnectionFactory;
import org.apache.hadoop.hbase.filter.FilterList;
import org.apache.hadoop.hbase.filter.PrefixFilter;
import org.apache.hadoop.hbase.filter.SingleColumnValueFilter;
import org.apache.hadoop.hbase.filter.CompareFilter;
import org.apache.hadoop.hbase.util.Bytes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.apache.hadoop.hbase.client.Get;
import org.apache.hadoop.hbase.client.Result;
import org.apache.hadoop.hbase.client.Table;
import org.apache.hadoop.hbase.TableName;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.io.IOException;

/**
 * HBase设备数据通用服务
 * 提供各类设备数据的通用查询功能
 *
 * <AUTHOR>
 */
@Service
public class HBaseDeviceDataService {
    /**
     * 日志记录器
     */
    private static final Logger logger = LoggerFactory.getLogger(HBaseDeviceDataService.class);

    /**
     * HBase 命名空间
     */
    private static final String NAMESPACE = "snct";

    /**
     * 时间列族和列名
     */
    private static final String TIME_FAMILY = "i";
    private static final String TIME_QUALIFIER = "i_b_t";

    /**
     * HBase 数据访问工具类
     */
    @Autowired
    private HBaseDaoUtil hBaseDaoUtil;

    /**
     * 线程安全的日期格式化对象
     */
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyyMMdd"));

    private static final ThreadLocal<SimpleDateFormat> TIME_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("HHmmss"));

    private static final ThreadLocal<SimpleDateFormat> DATETIME_FORMAT =
            ThreadLocal.withInitial(() -> new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"));

    /**
     * 基于游标的分页查询
     * 通过记录最后一个rowKey作为下一页的起始点，实现高效分页
     * 适用于所有类型的设备数据查询
     *
     * @param deviceType    设备类型 (modem, amplifier, pdu, pdu_out)
     * @param deptId        部门ID（null表示查询所有部门，非管理员会被限制为自己部门）
     * @param sn            船舶SN（可选）
     * @param deviceId      设备ID（可选）
     * @param startDateTime 开始日期时间（可选，格式yyyy-MM-dd HH:mm:ss）
     * @param endDateTime   结束日期时间（可选，格式yyyy-MM-dd HH:mm:ss）
     * @param limit         每页记录数
     * @param lastRowKey    上一页最后一条记录的RowKey，首次查询传null
     * @param isAdmin       是否为管理员，影响查询权限
     * @param voClass       返回对象的类，例如ModemHbaseVo.class
     * @return 包含数据列表和下一页标记的结果
     */
    public <T> Map<String, Object> queryCursorPagination(
            String deviceType, Long deptId, String sn, Long deviceId,
            String startDateTime, String endDateTime,
            int limit, String lastRowKey, boolean isAdmin, Class<T> voClass) {

        Map<String, Object> result = new HashMap<>();
        List<T> dataList = new ArrayList<>();
        String nextCursor = null;
        boolean hasMore = false;

        try {
            // 日志记录查询参数
            logger.debug("游标分页查询: 设备类型={}, deptId={}, sn={}, deviceId={}, 时间范围={}-{}, limit={}, lastRowKey={}, 是否管理员={}",
                    deviceType, deptId, sn, deviceId, startDateTime, endDateTime,
                    limit, lastRowKey, isAdmin);

            // 获取表名
            String tableName = getTableName(deviceType);

            // 创建一个空的VO对象用于查询
            T vo;
            try {
                vo = voClass.newInstance();
            } catch (Exception e) {
                logger.error("创建VO对象实例失败: {}", e.getMessage(), e);
                throw new RuntimeException("创建查询对象失败: " + e.getMessage());
            }

            // 判断是否有时间范围查询条件
            boolean hasTimeRange = (startDateTime != null && !startDateTime.isEmpty())
                    || (endDateTime != null && !endDateTime.isEmpty());

            // 查询多一条用于判断是否有下一页
            int fetchSize = limit + 1;

            // 根据是否有时间范围条件选择不同的查询方式
            List<T> queryResult;

            if (hasTimeRange) {
                // 构建查询使用的过滤器
                FilterList filterList = buildFilterList(deptId, sn, deviceId,
                        startDateTime, endDateTime,
                        isAdmin);

                // 如果有上一页游标，则从游标之后开始
                if (lastRowKey != null && !lastRowKey.isEmpty()) {
                    // 获取下一行的RowKey作为起始行键
                    String startKey = lastRowKey + "\u0000";
                    logger.info("使用游标作为起始行: {}", startKey);

                    // 使用复合过滤器查询
                    queryResult = hBaseDaoUtil.scanWithFilterList(vo, tableName,
                            startKey, null, filterList, fetchSize);
                } else {
                    // 首次查询
                    queryResult = hBaseDaoUtil.scanWithFilterList(vo, tableName,
                            null, null, filterList, fetchSize);
                }
            } else {
                // 无时间范围条件，使用RowKey范围查询
                String startRowKey = buildStartRowKey(deptId, sn, deviceId, null, isAdmin);
                String endRowKey = buildEndRowKey(deptId, sn, deviceId, null, isAdmin);

                // 如果有上一页游标，则从游标之后开始
                if (lastRowKey != null && !lastRowKey.isEmpty()) {
                    startRowKey = lastRowKey + "\u0000";
                    logger.debug("使用游标作为起始行: {}", startRowKey);
                }

                logger.debug("构建查询范围: 表名={}, startRowKey={}, endRowKey={}",
                        tableName, startRowKey, endRowKey);

                // 执行范围查询
                queryResult = hBaseDaoUtil.scanWithPageFilter(vo, tableName,
                        startRowKey, endRowKey, fetchSize);
            }

            logger.debug("HBase查询返回结果数量: {}", queryResult != null ? queryResult.size() : 0);

            // 处理查询结果
            if (queryResult != null && !queryResult.isEmpty()) {
                if (queryResult.size() > limit) {
                    // 有下一页数据
                    hasMore = true;

                    // 获取当页最后一条记录作为下一页游标
                    T lastRecord = queryResult.get(limit - 1);

                    // 通过反射获取ID字段值作为游标
                    try {
                        nextCursor = (String) voClass.getMethod("getId").invoke(lastRecord);
                    } catch (Exception e) {
                        logger.error("获取游标失败: {}", e.getMessage(), e);
                        nextCursor = null;
                    }

                    // 只返回limit条记录
                    dataList.addAll(queryResult.subList(0, limit));
                    logger.debug("数据有下一页，设置游标: {}, 本页记录数: {}", nextCursor, limit);
                } else {
                    // 不足一页，全部返回，无下一页
                    dataList.addAll(queryResult);
                    hasMore = false;
                    logger.info("数据已到末尾，无下一页，返回记录数: {}", dataList.size());
                }
            } else {
                logger.warn("HBase查询未返回结果");
            }
        } catch (Exception e) {
            logger.error("游标分页查询发生异常: {}", e.getMessage(), e);
        }

        // 构建返回结果
        result.put("data", dataList);
        result.put("cursor", nextCursor);
        result.put("hasMore", hasMore);
        result.put("size", dataList.size());

        return result;
    }

    /**
     * 构建复合过滤器
     *
     * @param deptId        部门ID
     * @param sn            船舶SN
     * @param deviceId      设备ID
     * @param startDateTime 开始日期时间
     * @param endDateTime   结束日期时间
     * @param isAdmin       是否管理员
     * @return 过滤器列表
     */
    private FilterList buildFilterList(Long deptId, String sn, Long deviceId,
                                       String startDateTime, String endDateTime,
                                       boolean isAdmin) {
        FilterList filterList = new FilterList(FilterList.Operator.MUST_PASS_ALL);

        // 添加前缀过滤器
        String prefix = buildPrefixKey(deptId, sn, deviceId, isAdmin);
        if (prefix != null && !prefix.isEmpty()) {
            filterList.addFilter(new PrefixFilter(Bytes.toBytes(prefix)));
            logger.info("添加前缀过滤器: {}", prefix);
        }

        // 添加时间范围过滤器
        if (startDateTime != null && !startDateTime.isEmpty()) {
            SingleColumnValueFilter startTimeFilter = new SingleColumnValueFilter(
                    Bytes.toBytes(TIME_FAMILY),
                    Bytes.toBytes(TIME_QUALIFIER),
                    CompareFilter.CompareOp.GREATER_OR_EQUAL,
                    Bytes.toBytes(startDateTime)
            );
            startTimeFilter.setFilterIfMissing(true);
            filterList.addFilter(startTimeFilter);
            logger.info("添加开始时间过滤器: >= {}", startDateTime);
        }

        if (endDateTime != null && !endDateTime.isEmpty()) {
            SingleColumnValueFilter endTimeFilter = new SingleColumnValueFilter(
                    Bytes.toBytes(TIME_FAMILY),
                    Bytes.toBytes(TIME_QUALIFIER),
                    CompareFilter.CompareOp.LESS_OR_EQUAL,
                    Bytes.toBytes(endDateTime)
            );
            endTimeFilter.setFilterIfMissing(true);
            filterList.addFilter(endTimeFilter);
            logger.info("添加结束时间过滤器: <= {}", endDateTime);
        }

        return filterList;
    }

    /**
     * 构建用于前缀过滤的key
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param isAdmin  是否管理员
     * @return 前缀键
     */
    private String buildPrefixKey(Long deptId, String sn, Long deviceId, boolean isAdmin) {
        StringBuilder prefix = new StringBuilder();

        // 未指定部门ID且非管理员，无法构建有效前缀
        if (deptId == null && !isAdmin) {
            return "";
        }

        // 指定了部门ID
        if (deptId != null) {
            prefix.append(String.format("%05d", deptId));

            // 如果还指定了船舶SN
            if (sn != null && !sn.isEmpty()) {
                prefix.append("_").append(sn);

                // 如果还指定了设备ID
                if (deviceId != null) {
                    prefix.append("_").append(String.format("%05d", deviceId));
                }
            }
        }

        return prefix.toString();
    }

    /**
     * 格式化日期和时间为查询格式
     *
     * @param dateYYYYMMDD 日期（格式：yyyyMMdd）
     * @param timeHHMMSS   时间（格式：HHmmss），如果为null则使用默认值
     * @return 格式化后的日期时间字符串
     */
    private String formatDateTimeForQuery(String dateYYYYMMDD, String timeHHMMSS) {
        try {
            String defaultTime = (timeHHMMSS != null && !timeHHMMSS.isEmpty()) ?
                    timeHHMMSS : "000000";

            // 解析日期部分
            Date date = DATE_FORMAT.get().parse(dateYYYYMMDD);

            // 解析时间部分
            Date time = TIME_FORMAT.get().parse(defaultTime);

            // 合并日期和时间
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);

            Calendar timeCal = Calendar.getInstance();
            timeCal.setTime(time);

            calendar.set(Calendar.HOUR_OF_DAY, timeCal.get(Calendar.HOUR_OF_DAY));
            calendar.set(Calendar.MINUTE, timeCal.get(Calendar.MINUTE));
            calendar.set(Calendar.SECOND, timeCal.get(Calendar.SECOND));

            // 返回格式化的日期时间
            return DATETIME_FORMAT.get().format(calendar.getTime());
        } catch (ParseException e) {
            logger.error("日期时间格式化错误: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 获取单条设备数据详情
     *
     * @param deviceType 设备类型 (modem, amplifier, pdu, pdu_out)
     * @param rowKey     数据行键
     * @param voClass    返回对象的类，例如ModemHbaseVo.class
     * @return 设备数据详情
     */
    public <T> T getDeviceDetail(String deviceType, String rowKey, Class<T> voClass) {
        try {
            // 获取表名
            String tableName = getTableName(deviceType);

            // 创建一个空的VO对象用于查询
            T vo = voClass.newInstance();

            // 使用HBaseDaoUtil的get方法查询单条数据
            List<T> resultList = hBaseDaoUtil.get(vo, rowKey);

            if (resultList != null && !resultList.isEmpty()) {
                return resultList.get(0);
            }

            return null;
        } catch (Exception e) {
            logger.error("获取设备数据详情时发生异常: 设备类型={}, rowKey={}, 错误={}",
                    deviceType, rowKey, e.getMessage(), e);
            return null;
        }
    }

    /**
     * 根据设备类型获取表名
     *
     * @param deviceType 设备类型
     * @return 表名
     */
    private String getTableName(String deviceType) {
        if (deviceType == null || deviceType.isEmpty()) {
            throw new IllegalArgumentException("设备类型不能为空");
        }

        return NAMESPACE + ":" + deviceType;
    }

    /**
     * 构建起始RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId    部门ID
     * @param sn        船舶SN
     * @param deviceId  设备ID
     * @param startDate 开始日期
     * @param isAdmin   是否为管理员
     * @return 起始RowKey
     */
    private String buildStartRowKey(Long deptId, String sn, Long deviceId, String startDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("00000");
        } else {
            // 必须有部门ID
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用起始值
            builder.append("000000");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用起始值
            builder.append("00000");
        }

        // 添加时间范围
        builder.append("_");
        if (startDate != null && !startDate.isEmpty()) {
            builder.append(startDate).append("_000000");
        } else {
            // 未指定开始日期，从最早的数据开始
            builder.append("00000000_000000");
        }

        return builder.toString();
    }

    /**
     * 构建结束RowKey
     * 根据查询设计文档实现各种条件查询
     *
     * @param deptId   部门ID
     * @param sn       船舶SN
     * @param deviceId 设备ID
     * @param endDate  结束日期
     * @param isAdmin  是否为管理员
     * @return 结束RowKey
     */
    private String buildEndRowKey(Long deptId, String sn, Long deviceId, String endDate, boolean isAdmin) {
        StringBuilder builder = new StringBuilder();

        // 管理员可以查询所有数据，非管理员必须指定部门
        if (isAdmin && deptId == null) {
            // 管理员查询所有部门的情况
            builder.append("99999");
        } else {
            // 必须有部门ID 
            Long effectiveDeptId = (deptId != null) ? deptId : 0L;
            builder.append(String.format("%05d", effectiveDeptId));
        }

        // 添加船舶SN
        builder.append("_");
        if (sn != null && !sn.isEmpty()) {
            builder.append(sn);
        } else {
            // 未指定船舶SN，使用结束值
            builder.append("zzzzzz");
        }

        // 添加设备ID
        builder.append("_");
        if (deviceId != null) {
            builder.append(String.format("%05d", deviceId));
        } else {
            // 未指定设备ID，使用结束值
            builder.append("99999");
        }

        // 添加时间范围
        builder.append("_");
        if (endDate != null && !endDate.isEmpty()) {
            builder.append(endDate).append("_235959");
        } else {
            // 未指定结束日期，到最新的数据为止
            builder.append("99999999_999999");
        }

        return builder.toString();
    }

    /**
     * 获取设备通道数据
     *
     * @param rowKey 数据行键
     * @return 通道数据映射，键为通道索引，值为通道数据对象
     */
    public Map<String, Object> getDeviceChannelData(String deviceType, String rowKey) {
        if (!"pdu".equals(deviceType)) {
            logger.warn("获取通道数据仅支持PDU设备，当前设备类型: {}", deviceType);
            return new HashMap<>();
        }

        Map<String, Object> channelDataMap = new HashMap<>();

        try {
            // 获取表名
            String tableName = getTableName(deviceType);

            // 获取PDU对象详情
            PduHbaseVo pduVo = getDeviceDetail(deviceType, rowKey, PduHbaseVo.class);
            if (pduVo == null) {
                logger.warn("未找到指定的PDU设备数据: rowKey={}", rowKey);
                return channelDataMap;
            }

            // 获取通道数据
            Map<Long, PduHbaseVo.ChannelData> channelDataObj = pduVo.getChannelDataMap();
            if (channelDataObj != null && !channelDataObj.isEmpty()) {
                // 转换通道数据格式
                for (Map.Entry<Long, PduHbaseVo.ChannelData> entry : channelDataObj.entrySet()) {
                    Long channelIndex = entry.getKey();
                    PduHbaseVo.ChannelData channelData = entry.getValue();

                    Map<String, Object> dataMap = new HashMap<>();
                    dataMap.put("outIndex", channelData.getOutIndex());
                    dataMap.put("electric", channelData.getElectric());
                    dataMap.put("power", channelData.getPower());
                    dataMap.put("outStatus", channelData.getOutStatus());

                    channelDataMap.put(channelIndex.toString(), dataMap);
                }

                return channelDataMap;
            }

            // 获取表的所有列簇
            List<String> families = hBaseDaoUtil.familys(tableName);

            // 从HBase直接获取通道数据
            Table table = null;
            try {
                table = HConnectionFactory.connection.getTable(TableName.valueOf(tableName));
                Get get = new Get(Bytes.toBytes(rowKey));

                // 只获取通道列簇
                for (String family : families) {
                    if (family.startsWith("o")) {
                        get.addFamily(Bytes.toBytes(family));
                    }
                }

                Result result = table.get(get);
                if (!result.isEmpty()) {
                    // 处理获取的通道数据
                    for (String family : families) {
                        if (family.startsWith("o") && family.length() <= 3) {
                            try {
                                int channelIndex = Integer.parseInt(family.substring(1));

                                Map<String, Object> dataMap = new HashMap<>();
                                dataMap.put("outIndex", (long) channelIndex);

                                // 获取电流值
                                byte[] electricBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("e_t"));
                                if (electricBytes != null) {
                                    dataMap.put("electric", Double.parseDouble(Bytes.toString(electricBytes)));
                                } else {
                                    dataMap.put("electric", 0.0);
                                }

                                // 获取功率值
                                byte[] powerBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("p_w"));
                                if (powerBytes != null) {
                                    dataMap.put("power", Double.parseDouble(Bytes.toString(powerBytes)));
                                } else {
                                    dataMap.put("power", 0.0);
                                }

                                // 获取插座状态
                                byte[] statusBytes = result.getValue(Bytes.toBytes(family), Bytes.toBytes("o_s"));
                                if (statusBytes != null) {
                                    dataMap.put("outStatus", Long.parseLong(Bytes.toString(statusBytes)));
                                } else {
                                    dataMap.put("outStatus", 0L);
                                }

                                channelDataMap.put(String.valueOf(channelIndex), dataMap);
                            } catch (NumberFormatException e) {
                                logger.warn("处理通道索引发生异常: {}, 列簇={}", e.getMessage(), family);
                            }
                        }
                    }

                    if (!channelDataMap.isEmpty()) {
                        return channelDataMap;
                    }
                }
            } finally {
                if (table != null) {
                    try {
                        table.close();
                    } catch (IOException e) {
                        logger.error("关闭HBase表连接时出错: {}", e.getMessage(), e);
                    }
                }
            }

        } catch (Exception e) {
            logger.error("获取设备通道数据时发生异常: 设备类型={}, rowKey={}, 错误={}",
                    deviceType, rowKey, e.getMessage(), e);
        }

        return channelDataMap;
    }
} 