package com.snct.hbase.utils;

import java.util.ArrayList;
import java.util.Random;

/**
 * class
 *
 * <AUTHOR>
 */
public class RandomUtil {

    private static ArrayList<String> strList = new ArrayList<String>();
    private static Random random = new Random();

    static {
        init();
    }

    public static String randomStr(int length) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < length; i++) {
            int size = strList.size();
            String randomStr = strList.get(random.nextInt(size));
            sb.append(randomStr);
        }
        return sb.toString();
    }

    private static void init() {
        int begin = 97;
        //生成小写字母,并加入集合
        for (int i = begin; i < begin + 26; i++) {
            strList.add((char) i + "");
        }
        //生成大写字母,并加入集合
        begin = 65;
        for (int i = begin; i < begin + 26; i++) {
            strList.add((char) i + "");
        }
        //将0-9的数字加入集合
        for (int i = 0; i < 10; i++) {
            strList.add(i + "");
        }
    }
}
