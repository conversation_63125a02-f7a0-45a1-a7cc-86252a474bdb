package com.snct.hbase.enums;

/**
 * <AUTHOR>
 */
public enum  EngineroomEnum {
    /**
     * 机舱数据 code
     */
    BallastWater(81, "压载水系统"),
    BoilerFeedWater(82, "锅炉给水系统"),
    CompressedAir(83, "压缩空气系统"),
    DieselGenerator1(84, "1号发动机"),
    DieselGenerator2(85, "2号发动机"),
    DieselGenerator3(86, "3号发动机"),
    EmergencyGenerator(87, "应急发动机"),
    FwGenerator(88, "造水机"),
    MainThruster4(89, "主推4"),
    ENGINE_ROOM_CONFIG(90, "机舱数据结构"),
    ENGINE_ROOM_DATA(91, "机舱数据"),
    FuelOil(92,"燃油系统"),
    SwCooling(93,"海水冷却系统"),
    PowerManage(94,"电力管理系统"),
    Propulsion(95,"主推2"),
    TunnelThruster(96,"侧推");
    private int  value;
    private String alias;

    EngineroomEnum(int value, String alias) {
        this.value = value;
        this.alias = alias;
    }

    public Integer getValue() {
        return value;
    }

    public String getAlias() {
        return alias;
    }
}
