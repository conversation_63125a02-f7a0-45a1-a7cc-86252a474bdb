package com.snct.hbase.domain.device;

import com.snct.hbase.annotation.Excel;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**解析预览对象
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2021/10/20.
 */
public class ParseViewEntity {
    /**
     * 解析后的数据集合
     */
    private List<Object> objectValueList;
    /**
     * 解析后的参数列表 参数属性中文名称-参数属性英文名称
     */
    private List<Map<String,String>> objectTitleList;

    public List<Object> getObjectValueList() {
        return objectValueList;
    }

    public void setObjectValueList(List<Object> objectValueList) {
        this.objectValueList = objectValueList;
    }

    public List<Map<String, String>> getObjectTitleList() {
        return objectTitleList;
    }

    public void setObjectTitleList(List<Map<String, String>> objectTitleList) {
        this.objectTitleList = objectTitleList;
    }

    public static void main(String[] args) throws NoSuchFieldException {
        ParseViewEntity parseViewEntity = new ParseViewEntity();
        Field[] fields = parseViewEntity.getClass().getDeclaredFields();

        for (Field field : fields) {
            try {
                if (org.apache.commons.lang3.StringUtils.isNotBlank(field.get(parseViewEntity)+"")){
                    //不为空的才加进去属性
                    ParseViewEntity hproperty = new ParseViewEntity();
                    Excel excel = field.getAnnotation(Excel.class);

                }

            } catch (IllegalAccessException e) {
             }

        }
    }
}
