package com.snct.task;

import com.snct.system.service.ISysSysRestartService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *系统任务
 * <AUTHOR>
 */
@Component("systemTask")
public class SystemTask {

    private static final Logger logger = LoggerFactory.getLogger(SystemTask.class);

    @Autowired
    private ISysSysRestartService sysRestartService;

    /**
     *定时重启操作系统任务
     */
    public void restartTask() {
        try {
            //logger.info(" >>> 执行重启任务 >>> ");
            sysRestartService.checkAndExecuteRestartTasks();
        } catch (Exception e) {
            logger.error("执行定时重启检查任务异常", e);
        }
    }
} 