package com.snct.utils;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.zip.GZIPInputStream;
import java.util.zip.GZIPOutputStream;
 
public class GzipUtil {
 
    public static byte[] compress(byte[] data) throws IOException {
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPOutputStream gzipOutputStream = new GZIPOutputStream(byteArrayOutputStream)) {
            gzipOutputStream.write(data);
        }
        return byteArrayOutputStream.toByteArray();
    }


    public static byte[] decompress(byte[] compressedData) throws IOException {
        ByteArrayInputStream byteArrayInputStream = new ByteArrayInputStream(compressedData);
        ByteArrayOutputStream byteArrayOutputStream = new ByteArrayOutputStream();
        try (GZIPInputStream gzipInputStream = new GZIPInputStream(byteArrayInputStream)) {
            byte[] buffer = new byte[1024];
            int len;
            while ((len = gzipInputStream.read(buffer)) > 0) {
                byteArrayOutputStream.write(buffer, 0, len);
            }
        }
        return byteArrayOutputStream.toByteArray();
    }


    public static void main(String[] args) {
        //String originalString = "这是一段需要被压缩的文本内容。这是一段需要被压缩的文本内容。这是一段需要被压缩的文本内容。这是一段需要被压缩的文本内容。这是一段需要被压缩的文本内容。这是一段需要被压缩的文本内容。";
        String originalString = "{\"name1\": \"Jo2hn\", \"addr1ess\": {\"ci3ty\": \"Par5is\"}, \"addr1ess\": {\"cit12y\": \"Pari1s\"}, \"addre3ss\": {\"ci123ty\": \"Par123is\"}, \"add4ress\": {\"ci4ty\": \"Par1is\"}, \"addr46ess\": {\"ci54ty\": \"Pa54ris\"}, \"add8r6ess\": {\"cit7y\": \"Par343is\"}, \"add0ress\": {\"ci435ty\": \"Par352is\"}}";
        System.out.println("压缩前 size: " + originalString.getBytes().length + " bytes"); // 输出压缩后的数据大小
        byte[] originalData = originalString.getBytes(); // 转换为字节数组
        try {
            byte[] compressedData = GzipUtil.compress(originalData); // 压缩数据

            System.out.println("Compressed size: " + compressedData.length + " bytes"); // 输出压缩后的数据大小

            byte[] decompressedData = GzipUtil.decompress(compressedData); // 解压缩数据
            System.out.println("Decompressed content: " + new String(decompressedData)); // 输出解压后的内容
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


}