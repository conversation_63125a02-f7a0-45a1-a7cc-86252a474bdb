package com.snct.hbase.domain.hbase;

import com.snct.hbase.annotation.Excel;
import com.snct.hbase.annotation.HBaseColumn;
import com.snct.hbase.annotation.HBaseTable;


/**
 * 功放消息对象 bu_msg_amplifier
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
@HBaseTable(tableName = "snct:amplifier")
public class AmplifierHbaseVo
{

    /** ID */
    @HBaseColumn(family = "rowkey", qualifier = "rowkey")
    private String id;
    /**
     * 录入时间(北京时间yyyy-MM-dd HH:mm:ss)
     */
    @Excel(name="录入时间")
    @HBaseColumn(family = "i", qualifier = "i_b_t")
    private String initialBjTime;

    /** 部门ID */
    @HBaseColumn(family = "i", qualifier = "d_p")
    private Long deptId;

    /** 部门名称 */
    @Excel(name="部门名称")
    @HBaseColumn(family = "i", qualifier = "d_p_n")
    private String deptName;

    /** 船只id */
    @HBaseColumn(family = "i", qualifier = "s_i")
    private Long shipId;

    /** 船只名称 */
    @Excel(name="船只名称")
    @HBaseColumn(family = "i", qualifier = "s_n")
    private String shipName;

    /** 设备id */
    @HBaseColumn(family = "i", qualifier = "d_i")
    private Long deviceId;

    /** 设备名称 */
    @Excel(name="设备名称")
    @HBaseColumn(family = "i", qualifier = "d_n")
    private String deviceName;

    /** 衰减值 */
    @Excel(name = "衰减值")
    @HBaseColumn(family = "i", qualifier = "d_c")
    private Double decay;

    /** 温度 */
    @Excel(name = "温度")
    @HBaseColumn(family = "i", qualifier = "t_p")
    private Double temp;

    /** 输出功率 */
    @Excel(name = "输出功率")
    @HBaseColumn(family = "i", qualifier = "o_p")
    private Double outPower;

    /** 设备状态 */
    @Excel(name = "设备状态")
    @HBaseColumn(family = "i", qualifier = "b_s")
    private Long bucStatus;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    @HBaseColumn(family = "i", qualifier = "s_t")
    private Long status;

    public void setId(String id)
    {
        this.id = id;
    }

    public String getId()
    {
        return id;
    }

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setShipId(Long shipId)
    {
        this.shipId = shipId;
    }

    public Long getShipId()
    {
        return shipId;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public String getDeptName() {
        return deptName;
    }

    public void setDeptName(String deptName) {
        this.deptName = deptName;
    }

    public String getShipName() {
        return shipName;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getDeviceName() {
        return deviceName;
    }

    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }

    public void setDecay(Double decay)
    {
        this.decay = decay;
    }

    public Double getDecay()
    {
        return decay;
    }

    public void setTemp(Double temp)
    {
        this.temp = temp;
    }

    public Double getTemp()
    {
        return temp;
    }

    public void setOutPower(Double outPower)
    {
        this.outPower = outPower;
    }

    public Double getOutPower()
    {
        return outPower;
    }

    public void setBucStatus(Long bucStatus)
    {
        this.bucStatus = bucStatus;
    }

    public Long getBucStatus()
    {
        return bucStatus;
    }

    public void setStatus(Long status)
    {
        this.status = status;
    }

    public Long getStatus()
    {
        return status;
    }

    public String getInitialBjTime() {
        return initialBjTime;
    }

    public void setInitialBjTime(String initialBjTime) {
        this.initialBjTime = initialBjTime;
    }
}
