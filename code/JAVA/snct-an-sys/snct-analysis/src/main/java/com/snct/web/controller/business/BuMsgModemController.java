//package com.snct.web.controller.business;
//
//import com.snct.common.annotation.Log;
//import com.snct.common.core.controller.BaseController;
//import com.snct.common.core.domain.AjaxResult;
//import com.snct.common.core.page.TableDataInfo;
//import com.snct.common.enums.BusinessType;
//import com.snct.common.utils.poi.ExcelUtil;
//import com.snct.system.domain.msg.BuMsgModem;
//import com.snct.system.service.IBuMsgModemService;
//import org.springframework.beans.factory.annotation.Autowired;
//import org.springframework.security.access.prepost.PreAuthorize;
//import org.springframework.web.bind.annotation.*;
//
//import javax.servlet.http.HttpServletResponse;
//import java.util.List;
//
///**
// * Modem消息Controller
// *
// * <AUTHOR>
// * @date 2025-04-24
// */
//@RestController
//@RequestMapping("/devicemsg/modem")
//public class BuMsgModemController extends BaseController
//{
//    @Autowired
//    private IBuMsgModemService buMsgModemService;
//
//    /**
//     * 查询Modem消息列表
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:list')")
//    @GetMapping("/list")
//    public TableDataInfo list(BuMsgModem buMsgModem)
//    {
//        startPage();
//        List<BuMsgModem> list = buMsgModemService.selectBuMsgModemList(buMsgModem);
//        return getDataTable(list);
//    }
//
//    /**
//     * 导出Modem消息列表
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:export')")
//    @Log(title = "Modem消息", businessType = BusinessType.EXPORT)
//    @PostMapping("/export")
//    public void export(HttpServletResponse response, BuMsgModem buMsgModem)
//    {
//        List<BuMsgModem> list = buMsgModemService.selectBuMsgModemList(buMsgModem);
//        ExcelUtil<BuMsgModem> util = new ExcelUtil<BuMsgModem>(BuMsgModem.class);
//        util.exportExcel(response, list, "Modem消息数据");
//    }
//
//    /**
//     * 获取Modem消息详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return success(buMsgModemService.selectBuMsgModemById(id));
//    }
//
//    /**
//     * 新增Modem消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:add')")
//    @Log(title = "Modem消息", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody BuMsgModem buMsgModem)
//    {
//        return toAjax(buMsgModemService.insertBuMsgModem(buMsgModem));
//    }
//
//    /**
//     * 修改Modem消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:edit')")
//    @Log(title = "Modem消息", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody BuMsgModem buMsgModem)
//    {
//        return toAjax(buMsgModemService.updateBuMsgModem(buMsgModem));
//    }
//
//    /**
//     * 删除Modem消息
//     */
//    @PreAuthorize("@ss.hasPermi('devicemsg:modem:remove')")
//    @Log(title = "Modem消息", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids)
//    {
//        return toAjax(buMsgModemService.deleteBuMsgModemByIds(ids));
//    }
//}
