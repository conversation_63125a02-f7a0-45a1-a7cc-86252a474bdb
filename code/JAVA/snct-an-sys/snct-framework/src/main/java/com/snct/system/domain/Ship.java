package com.snct.system.domain;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 船只对象 bu_ship
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public class Ship extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 船只ID */
    private Long shipId;

    /**采集端船只ID**/
    private Long daShipId;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** sn */
    @Excel(name = "sn")
    private String sn;

    /** 船只名称 */
    @Excel(name = "船只名称")
    private String name;

    /** MMSI */
    @Excel(name = "MMSI")
    private String mmsi;

    /** 呼号 */
    @Excel(name = "呼号")
    private String callSign;

    /** IMO */
    @Excel(name = "IMO")
    private String imo;

    /** 状态 */
    @Excel(name = "状态")
    private Long status;

    /** 属性json */
    @Excel(name = "属性json")
    private String moduleJson;

    public void setShipId(Long shipId) 
    {
        this.shipId = shipId;
    }

    public Long getShipId() 
    {
        return shipId;
    }

    public void setDaShipId(Long daShipId)
    {
        this.daShipId = daShipId;
    }

    public Long getDaShipId()
    {
        return daShipId;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public void setDeptName(String deptName) 
    {
        this.deptName = deptName;
    }

    public String getDeptName() 
    {
        return deptName;
    }

    public void setSn(String sn) 
    {
        this.sn = sn;
    }

    public String getSn() 
    {
        return sn;
    }

    public void setName(String name) 
    {
        this.name = name;
    }

    public String getName() 
    {
        return name;
    }

    public void setMmsi(String mmsi) 
    {
        this.mmsi = mmsi;
    }

    public String getMmsi() 
    {
        return mmsi;
    }

    public void setCallSign(String callSign) 
    {
        this.callSign = callSign;
    }

    public String getCallSign() 
    {
        return callSign;
    }

    public void setImo(String imo) 
    {
        this.imo = imo;
    }

    public String getImo() 
    {
        return imo;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    public void setModuleJson(String moduleJson) 
    {
        this.moduleJson = moduleJson;
    }

    public String getModuleJson() 
    {
        return moduleJson;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("shipId", getShipId())
            .append("daShipId", getDaShipId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("sn", getSn())
            .append("name", getName())
            .append("mmsi", getMmsi())
            .append("callSign", getCallSign())
            .append("imo", getImo())
            .append("status", getStatus())
            .append("remark", getRemark())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("moduleJson", getModuleJson())
            .toString();
    }
}
