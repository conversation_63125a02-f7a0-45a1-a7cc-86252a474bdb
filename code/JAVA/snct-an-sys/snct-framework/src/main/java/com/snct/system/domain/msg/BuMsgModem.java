package com.snct.system.domain.msg;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;


/**
 * Modem消息对象 bu_msg_modem
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public class BuMsgModem extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** ID */
    private Long id;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    /** 部门名称 */
    @Excel(name = "部门名称")
    private String deptName;

    /** 船舶名称 */
    @Excel(name = "船舶名称")
    private String shipName;

    /** 船只id */
    @Excel(name = "船只id")
    private Long shipId;

    /** 设备id */
    @Excel(name = "设备id")
    private Long deviceId;

    /** 信号强度 */
    @Excel(name = "信号强度")
    private Double signal;

    /** 速率 */
    @Excel(name = "速率")
    private Double speed;

    /** 发送功率 */
    @Excel(name = "发送功率")
    private Double sendPower;

    /** 状态标志 */
    @Excel(name = "状态标志")
    private Long isFlag;

    /** 状态 0默认 1发送云端成功 2发送云端失败 */
    @Excel(name = "状态 0默认 1发送云端成功 2发送云端失败")
    private Long status;

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setDeptId(Long deptId) 
    {
        this.deptId = deptId;
    }

    public Long getDeptId() 
    {
        return deptId;
    }

    public String getDeptName()
    {
        return deptName;
    }

    public void setDeptName(String deptName)
    {
        this.deptName = deptName;
    }

    public void setShipId(Long shipId) 
    {
        this.shipId = shipId;
    }

    public Long getShipId() 
    {
        return shipId;
    }

    public String getShipName()
    {
        return shipName;
    }

    public void setShipName(String shipName)
    {
        this.shipName = shipName;
    }

    public void setDeviceId(Long deviceId)
    {
        this.deviceId = deviceId;
    }

    public Long getDeviceId()
    {
        return deviceId;
    }

    public void setSignal(Double signal) 
    {
        this.signal = signal;
    }

    public Double getSignal() 
    {
        return signal;
    }

    public void setSpeed(Double speed) 
    {
        this.speed = speed;
    }

    public Double getSpeed() 
    {
        return speed;
    }

    public void setSendPower(Double sendPower) 
    {
        this.sendPower = sendPower;
    }

    public Double getSendPower() 
    {
        return sendPower;
    }

    public void setIsFlag(Long isFlag) 
    {
        this.isFlag = isFlag;
    }

    public Long getIsFlag() 
    {
        return isFlag;
    }

    public void setStatus(Long status) 
    {
        this.status = status;
    }

    public Long getStatus() 
    {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("deptId", getDeptId())
            .append("deptName", getDeptName())
            .append("shipId", getShipId())
            .append("shipName", getShipName())
            .append("deviceId", getDeviceId())
            .append("signal", getSignal())
            .append("speed", getSpeed())
            .append("sendPower", getSendPower())
            .append("isFlag", getIsFlag())
            .append("status", getStatus())
            .append("createTime", getCreateTime())
            .toString();
    }
}
