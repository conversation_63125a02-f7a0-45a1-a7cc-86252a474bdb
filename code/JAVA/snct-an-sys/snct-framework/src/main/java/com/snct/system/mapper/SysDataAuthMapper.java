package com.snct.system.mapper;

import com.snct.system.domain.SysDataAuth;

import java.util.List;

/**
 * 数据权限Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-04-08
 */
public interface SysDataAuthMapper 
{
    /**
     * 查询数据权限
     * 
     * @param dataAuthId 数据权限主键
     * @return 数据权限
     */
    public SysDataAuth selectSysDataAuthByDataAuthId(Long dataAuthId);

    /**
     * 查询数据权限列表
     * 
     * @param sysDataAuth 数据权限
     * @return 数据权限集合
     */
    public List<SysDataAuth> selectSysDataAuthList(SysDataAuth sysDataAuth);

    /**
     * 新增数据权限
     * 
     * @param sysDataAuth 数据权限
     * @return 结果
     */
    public int insertSysDataAuth(SysDataAuth sysDataAuth);

    /**
     * 修改数据权限
     * 
     * @param sysDataAuth 数据权限
     * @return 结果
     */
    public int updateSysDataAuth(SysDataAuth sysDataAuth);

    /**
     * 删除数据权限
     * 
     * @param dataAuthId 数据权限主键
     * @return 结果
     */
    public int deleteSysDataAuthByDataAuthId(Long dataAuthId);

    /**
     * 批量删除数据权限
     * 
     * @param dataAuthIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSysDataAuthByDataAuthIds(Long[] dataAuthIds);


    /**
     * 校验数据权限名称
     *
     * @param dataAuthName 数据权限名称
     * @return 结果
     */
    public SysDataAuth checkDataAuthNameUnique(String dataAuthName);

}
