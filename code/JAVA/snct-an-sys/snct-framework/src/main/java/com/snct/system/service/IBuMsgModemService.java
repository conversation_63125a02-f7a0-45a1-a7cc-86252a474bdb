package com.snct.system.service;

import com.snct.system.domain.msg.BuMsgModem;

import java.util.List;

/**
 * Modem消息Service接口
 * 
 * <AUTHOR>
 * @date 2025-04-24
 */
public interface IBuMsgModemService 
{
    /**
     * 查询Modem消息
     * 
     * @param id Modem消息主键
     * @return Modem消息
     */
    public BuMsgModem selectBuMsgModemById(Long id);

    /**
     * 查询Modem消息列表
     * 
     * @param buMsgModem Modem消息
     * @return Modem消息集合
     */
    public List<BuMsgModem> selectBuMsgModemList(BuMsgModem buMsgModem);

    /**
     * 新增Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    public int insertBuMsgModem(BuMsgModem buMsgModem);

    /**
     * 修改Modem消息
     * 
     * @param buMsgModem Modem消息
     * @return 结果
     */
    public int updateBuMsgModem(BuMsgModem buMsgModem);

    /**
     * 批量删除Modem消息
     * 
     * @param ids 需要删除的Modem消息主键集合
     * @return 结果
     */
    public int deleteBuMsgModemByIds(Long[] ids);

    /**
     * 删除Modem消息信息
     * 
     * @param id Modem消息主键
     * @return 结果
     */
    public int deleteBuMsgModemById(Long id);
}
