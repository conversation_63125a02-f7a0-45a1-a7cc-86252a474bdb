package com.snct.system.domain;

import com.snct.common.annotation.Excel;
import com.snct.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 串口配置对象 bu_serial_config
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
public class SerialConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    private Long id;

    /** 原始串口名称 */
    @Excel(name = "原始串口名称")
    private String oldName;

    /** 新得系统使用名称 */
    @Excel(name = "新得系统使用名称")
    private String newName;

    /** 状态：（0：未启用，1：启用） */
    @Excel(name = "状态：", readConverterExp = "0=：未启用，1：启用")
    private Long enable;

    /** 部门ID */
    @Excel(name = "部门ID")
    private Long deptId;

    public void setDeptId(Long deptId)
    {
        this.deptId = deptId;
    }

    public Long getDeptId()
    {
        return deptId;
    }

    public void setId(Long id) 
    {
        this.id = id;
    }

    public Long getId() 
    {
        return id;
    }

    public void setOldName(String oldName) 
    {
        this.oldName = oldName;
    }

    public String getOldName() 
    {
        return oldName;
    }

    public void setNewName(String newName) 
    {
        this.newName = newName;
    }

    public String getNewName() 
    {
        return newName;
    }

    public void setEnable(Long enable) 
    {
        this.enable = enable;
    }

    public Long getEnable() 
    {
        return enable;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("oldName", getOldName())
            .append("newName", getNewName())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("enable", getEnable())
            .append("remark", getRemark())
            .toString();
    }
}
