package com.snct.system.service.impl;

import com.snct.common.utils.DateUtils;
import com.snct.system.domain.DeviceAttribute;
import com.snct.system.mapper.DeviceAttributeMapper;
import com.snct.system.service.IDeviceAttributeService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 设备属性Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-04-09
 */
@Service
public class DeviceAttributeServiceImpl implements IDeviceAttributeService 
{
    @Autowired
    private DeviceAttributeMapper deviceAttributeMapper;

    /**
     * 查询设备属性
     * 
     * @param id 设备属性主键
     * @return 设备属性
     */
    @Override
    public DeviceAttribute selectDeviceAttributeById(Long id)
    {
        return deviceAttributeMapper.selectDeviceAttributeById(id);
    }

    /**
     * 查询设备属性列表
     * 
     * @param deviceAttribute 设备属性
     * @return 设备属性
     */
    @Override
    public List<DeviceAttribute> selectDeviceAttributeList(DeviceAttribute deviceAttribute)
    {
        return deviceAttributeMapper.selectDeviceAttributeList(deviceAttribute);
    }

    /**
     * 新增设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    @Override
    public int insertDeviceAttribute(DeviceAttribute deviceAttribute)
    {
        deviceAttribute.setCreateTime(DateUtils.getNowDate());
        return deviceAttributeMapper.insertDeviceAttribute(deviceAttribute);
    }

    /**
     * 修改设备属性
     * 
     * @param deviceAttribute 设备属性
     * @return 结果
     */
    @Override
    public int updateDeviceAttribute(DeviceAttribute deviceAttribute)
    {
        deviceAttribute.setUpdateTime(DateUtils.getNowDate());
        return deviceAttributeMapper.updateDeviceAttribute(deviceAttribute);
    }

    /**
     * 批量删除设备属性
     * 
     * @param ids 需要删除的设备属性主键
     * @return 结果
     */
    @Override
    public int deleteDeviceAttributeByIds(Long[] ids)
    {
        return deviceAttributeMapper.deleteDeviceAttributeByIds(ids);
    }

    /**
     * 删除设备属性信息
     * 
     * @param id 设备属性主键
     * @return 结果
     */
    @Override
    public int deleteDeviceAttributeById(Long id)
    {
        return deviceAttributeMapper.deleteDeviceAttributeById(id);
    }
}
