<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.SysLogininforMapper">

	<resultMap type="SysLogininfor" id="SysLogininforResult">
		<id     property="infoId"        column="info_id"           />
		<result property="userName"      column="user_name"         />
		<result property="status"        column="status"            />
		<result property="ipaddr"        column="ipaddr"            />
		<result property="loginLocation" column="login_location"    />
		<result property="browser"       column="browser"           />
		<result property="os"            column="os"                />
		<result property="msg"           column="msg"               />
		<result property="loginTime"     column="login_time"        />
	</resultMap>

	<insert id="insertLogininfor" parameterType="SysLogininfor">
		insert into sys_log_login (user_name, status, ipaddr, login_location, browser, os, msg, login_time,dept_id)
		values (#{userName}, #{status}, #{ipaddr}, #{loginLocation}, #{browser}, #{os}, #{msg}, sysdate(),#{deptId})
	</insert>

	<select id="selectLogininforList" parameterType="SysLogininfor" resultMap="SysLogininforResult">
		select info_id, user_name, ipaddr, login_location, browser, os, status, msg, login_time from sys_log_login
		where 1=1
		<if test="ipaddr != null and ipaddr != ''">
			AND ipaddr like concat('%', #{ipaddr}, '%')
		</if>
		<if test="status != null and status != ''">
			AND status = #{status}
		</if>
		<if test="userName != null and userName != ''">
			AND user_name like concat('%', #{userName}, '%')
		</if>
		<if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
			AND login_time &gt;= #{params.beginTime}
		</if>
		<if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
			AND login_time &lt;= #{params.endTime}
		</if>

		${params.dataScope}
		order by info_id desc
	</select>

	<delete id="deleteLogininforByIds" parameterType="Long">
		delete from sys_log_login where info_id in
		<foreach collection="array" item="infoId" open="(" separator="," close=")">
			#{infoId}
		</foreach>
	</delete>

	<update id="cleanLogininfor">
		truncate table sys_log_login
	</update>

</mapper>