<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.DeviceMapper">

	<resultMap type="Device" id="DeviceResult">
		<result property="id"    column="id"    />
		<result property="daDeviceId"    column="da_device_id"    />
		<result property="deptId"    column="dept_id"    />
		<result property="deptName"    column="dept_name"    />
		<result property="sn"    column="sn"    />
		<result property="name"    column="name"    />
		<result property="type"    column="type"    />
		<result property="code"    column="code"    />
		<result property="cost"    column="cost"    />
		<result property="enable"    column="enable"    />
		<result property="mac"    column="mac"    />
		<result property="createBy"    column="create_by"    />
		<result property="createTime"    column="create_time"    />
		<result property="updateBy"    column="update_by"    />
		<result property="updateTime"    column="update_time"    />
		<result property="remark"    column="remark"    />
		<result property="transferStatus"    column="transfer_status"    />
		<result property="compartment"    column="compartment"    />
		<result property="connectType"    column="connect_type"    />
		<result property="baudRate"    column="baud_rate"    />
		<result property="dataBits"    column="data_bits"    />
		<result property="stopBits"    column="stop_bits"    />
		<result property="parity"    column="parity"    />
		<result property="serialPort"    column="serial_port"    />
		<result property="ip"    column="ip"    />
		<result property="port"    column="port"    />
		<result property="connectStatus"    column="connect_status"    />
	</resultMap>

	<sql id="selectDeviceVo">
		select d.id,d.da_device_id, d.dept_id, sd.dept_name, d.sn, d.name, d.type, d.code, d.cost, d.enable, d.mac, d.create_by,
		       d.create_time, d.update_by, d.update_time, d.remark, d.transfer_status, d.compartment, d.connect_type, 
		       d.baud_rate, d.data_bits, d.stop_bits, d.parity, d.serial_port, d.connect_status, d.ip, d.port
		from bu_device d
		left join sys_dept sd on d.dept_id = sd.dept_id
	</sql>

	<select id="selectDeviceList" parameterType="Device" resultMap="DeviceResult">
		<include refid="selectDeviceVo"/>
		<where>
			<if test="daDeviceId != null "> and d.da_device_id = #{daDeviceId}</if>
			<if test="deptId != null "> and d.dept_id = #{deptId}</if>
			<if test="sn != null  and sn != ''"> and d.sn = #{sn}</if>
			<if test="name != null  and name != ''"> and d.name like concat('%', #{name}, '%')</if>
			<if test="type != null "> and d.type = #{type}</if>
			<if test="code != null  and code != ''"> and d.code = #{code}</if>
			<if test="cost != null "> and d.cost = #{cost}</if>
			<if test="enable != null "> and d.enable = #{enable}</if>
			<if test="mac != null  and mac != ''"> and d.mac = #{mac}</if>
			<if test="transferStatus != null "> and d.transfer_status = #{transferStatus}</if>
			<if test="compartment != null "> and d.compartment = #{compartment}</if>
			<if test="connectType != null "> and d.connect_type = #{connectType}</if>
			<if test="baudRate != null "> and d.baud_rate = #{baudRate}</if>
			<if test="dataBits != null "> and d.data_bits = #{dataBits}</if>
			<if test="stopBits != null "> and d.stop_bits = #{stopBits}</if>
			<if test="parity != null "> and d.parity = #{parity}</if>
			<if test="serialPort != null  and serialPort != ''"> and d.serial_port = #{serialPort}</if>
			<if test="ip != null  and ip != ''"> and d.ip = #{ip}</if>
			<if test="port != null  and port != ''"> and d.port = #{port}</if>
			<if test="connectStatus != null "> and d.connect_status = #{connectStatus}</if>
		</where>
	</select>

	<select id="selectDeviceById" parameterType="Long" resultMap="DeviceResult">
		<include refid="selectDeviceVo"/>
		where d.id = #{id}
	</select>

	<insert id="insertDevice" parameterType="Device" useGeneratedKeys="true" keyProperty="id">
		insert into bu_device
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="daDeviceId != null">da_device_id,</if>
			<if test="deptId != null">dept_id,</if>
			<if test="sn != null and sn != ''">sn,</if>
			<if test="name != null and name != ''">name,</if>
			<if test="type != null">type,</if>
			<if test="code != null and code != ''">code,</if>
			<if test="cost != null">cost,</if>
			<if test="enable != null">enable,</if>
			<if test="mac != null">mac,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="remark != null">remark,</if>
			<if test="transferStatus != null">transfer_status,</if>
			<if test="compartment != null">compartment,</if>
			<if test="connectType != null">connect_type,</if>
			<if test="baudRate != null">baud_rate,</if>
			<if test="dataBits != null">data_bits,</if>
			<if test="stopBits != null">stop_bits,</if>
			<if test="parity != null">parity,</if>
			<if test="serialPort != null">serial_port,</if>
		    <if test="ip != null">ip,</if>
		    <if test="port != null">port,</if>
			<if test="connectStatus != null">connect_status,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="daDeviceId != null">#{daDeviceId},</if>
			<if test="deptId != null">#{deptId},</if>
			<if test="sn != null and sn != ''">#{sn},</if>
			<if test="name != null and name != ''">#{name},</if>
			<if test="type != null">#{type},</if>
			<if test="code != null and code != ''">#{code},</if>
			<if test="cost != null">#{cost},</if>
			<if test="enable != null">#{enable},</if>
			<if test="mac != null">#{mac},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="remark != null">#{remark},</if>
			<if test="transferStatus != null">#{transferStatus},</if>
			<if test="compartment != null">#{compartment},</if>
			<if test="connectType != null">#{connectType},</if>
			<if test="baudRate != null">#{baudRate},</if>
			<if test="dataBits != null">#{dataBits},</if>
			<if test="stopBits != null">#{stopBits},</if>
			<if test="parity != null">#{parity},</if>
			<if test="serialPort != null">#{serialPort},</if>
		    <if test="ip != null">#{ip},</if>
		    <if test="port != null">#{port},</if>
			<if test="connectStatus != null">#{connectStatus},</if>
		</trim>
	</insert>

	<update id="updateDevice" parameterType="Device">
		update bu_device
		<trim prefix="SET" suffixOverrides=",">
			<if test="daDeviceId != null">da_device_id = #{daDeviceId},</if>
			<if test="deptId != null">dept_id = #{deptId},</if>
			<if test="sn != null and sn != ''">sn = #{sn},</if>
			<if test="name != null and name != ''">name = #{name},</if>
			<if test="type != null">type = #{type},</if>
			<if test="code != null and code != ''">code = #{code},</if>
			<if test="cost != null">cost = #{cost},</if>
			<if test="enable != null">enable = #{enable},</if>
			<if test="mac != null">mac = #{mac},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="transferStatus != null">transfer_status = #{transferStatus},</if>
			<if test="compartment != null">compartment = #{compartment},</if>
			<if test="connectType != null">connect_type = #{connectType},</if>
			<if test="baudRate != null">baud_rate = #{baudRate},</if>
			<if test="dataBits != null">data_bits = #{dataBits},</if>
			<if test="stopBits != null">stop_bits = #{stopBits},</if>
			<if test="parity != null">parity = #{parity},</if>
			<if test="serialPort != null">serial_port = #{serialPort},</if>
		    <if test="ip != null">ip = #{ip},</if>
		    <if test="port != null">port = #{port},</if>
			<if test="connectStatus != null">connect_status = #{connectStatus},</if>
		</trim>
		where id = #{id}
	</update>

	<delete id="deleteDeviceById" parameterType="Long">
		delete from bu_device where id = #{id}
	</delete>

	<delete id="deleteDeviceByIds" parameterType="String">
		delete from bu_device where id in
		<foreach item="id" collection="array" open="(" separator="," close=")">
			#{id}
		</foreach>
	</delete>
</mapper>