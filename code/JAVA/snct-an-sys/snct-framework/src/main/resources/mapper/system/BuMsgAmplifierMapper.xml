<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.BuMsgAmplifierMapper">
    
    <resultMap type="BuMsgAmplifier" id="BuMsgAmplifierResult">
        <result property="id"    column="id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="deptName"  column="dept_name"  />
        <result property="shipId"    column="ship_id"    />
        <result property="shipName"  column="name"  />
        <result property="deviceId"    column="device_id"    />
        <result property="decay"    column="decay"    />
        <result property="temp"    column="temp"    />
        <result property="outPower"    column="outPower"    />
        <result property="bucStatus"    column="bucStatus"    />
        <result property="status"    column="status"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectBuMsgAmplifierVo">
        select a.id, a.dept_id, d.dept_name, s.name, a.ship_id, a.device_id, a.decay, a.temp, a.outPower, a.bucStatus, a.status, a.create_time
        from bu_msg_amplifier a
        left join sys_dept d on a.dept_id = d.dept_id
        left join bu_ship s on a.ship_id = s.ship_id
    </sql>

    <select id="selectBuMsgAmplifierList" parameterType="BuMsgAmplifier" resultMap="BuMsgAmplifierResult">
        <include refid="selectBuMsgAmplifierVo"/>
        <where>  
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="deptName != null and deptName != ''"> and d.dept_name like concat('%', #{deptName}, '%')</if>
            <if test="shipId != null "> and ship_id = #{shipId}</if>
            <if test="shipName != null and shipName != ''"> and s.name like concat('%', #{shipName}, '%')</if>
            <if test="deviceId != null "> and device_id = #{deviceId}</if>
            <if test="decay != null "> and decay = #{decay}</if>
            <if test="temp != null "> and temp = #{temp}</if>
            <if test="outPower != null "> and outPower = #{outPower}</if>
            <if test="bucStatus != null "> and bucStatus = #{bucStatus}</if>
            <if test="status != null "> and status = #{status}</if>
            ${params.dataScope}
        </where>
    </select>
    
    <select id="selectBuMsgAmplifierById" parameterType="Long" resultMap="BuMsgAmplifierResult">
        <include refid="selectBuMsgAmplifierVo"/>
        where id = #{id}
    </select>

    <insert id="insertBuMsgAmplifier" parameterType="BuMsgAmplifier" useGeneratedKeys="true" keyProperty="id">
        insert into bu_msg_amplifier
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="shipId != null">ship_id,</if>
            <if test="deviceId != null ">device_id,</if>
            <if test="decay != null">decay,</if>
            <if test="temp != null">temp,</if>
            <if test="outPower != null">outPower,</if>
            <if test="bucStatus != null">bucStatus,</if>
            <if test="status != null">status,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="shipId != null">#{shipId},</if>
            <if test="deviceId != null ">#{deviceId},</if>
            <if test="decay != null">#{decay},</if>
            <if test="temp != null">#{temp},</if>
            <if test="outPower != null">#{outPower},</if>
            <if test="bucStatus != null">#{bucStatus},</if>
            <if test="status != null">#{status},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateBuMsgAmplifier" parameterType="BuMsgAmplifier">
        update bu_msg_amplifier
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="shipId != null">ship_id = #{shipId},</if>
            <if test="deviceId != null ">device_id = #{deviceId},</if>
            <if test="decay != null">decay = #{decay},</if>
            <if test="temp != null">temp = #{temp},</if>
            <if test="outPower != null">outPower = #{outPower},</if>
            <if test="bucStatus != null">bucStatus = #{bucStatus},</if>
            <if test="status != null">status = #{status},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteBuMsgAmplifierById" parameterType="Long">
        delete from bu_msg_amplifier where id = #{id}
    </delete>

    <delete id="deleteBuMsgAmplifierByIds" parameterType="String">
        delete from bu_msg_amplifier where id in 
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>