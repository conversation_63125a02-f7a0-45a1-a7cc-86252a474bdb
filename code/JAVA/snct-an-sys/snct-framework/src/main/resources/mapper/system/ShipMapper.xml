<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
		PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
		"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.snct.system.mapper.ShipMapper">

	<resultMap type="Ship" id="ShipResult">
		<result property="shipId"    column="ship_id"    />
		<result property="daShipId"    column="da_ship_id"    />
		<result property="deptId"    column="dept_id"    />
		<result property="deptName"  column="dept_name"  />
		<result property="sn"    column="sn"    />
		<result property="name"    column="name"    />
		<result property="mmsi"    column="mmsi"    />
		<result property="callSign"    column="call_sign"    />
		<result property="imo"    column="imo"    />
		<result property="status"    column="status"    />
		<result property="remark"    column="remark"    />
		<result property="createBy"    column="create_by"    />
		<result property="createTime"    column="create_time"    />
		<result property="updateBy"    column="update_by"    />
		<result property="updateTime"    column="update_time"    />
		<result property="moduleJson"    column="module_json"    />
	</resultMap>

	<sql id="selectShipVo">
		select s.ship_id,s.da_ship_id, s.dept_id, d.dept_name, s.sn, s.name, s.mmsi, s.call_sign, s.imo, s.status,
		       s.remark, s.create_by, s.create_time, s.update_by, s.update_time, s.module_json 
		from bu_ship s
		left join sys_dept d on s.dept_id = d.dept_id
	</sql>

	<select id="selectShipList" parameterType="Ship" resultMap="ShipResult">
		<include refid="selectShipVo"/>
		<where>
			<if test="daShipId != null "> and da_ship_id = #{daShipId}</if>
			<if test="deptId != null "> and dept_id = #{deptId}</if>
			<if test="sn != null  and sn != ''"> and sn = #{sn}</if>
			<if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
			<if test="mmsi != null  and mmsi != ''"> and mmsi = #{mmsi}</if>
			<if test="callSign != null  and callSign != ''"> and call_sign = #{callSign}</if>
			<if test="imo != null  and imo != ''"> and imo = #{imo}</if>
			<if test="status != null "> and status = #{status}</if>
			<if test="moduleJson != null  and moduleJson != ''"> and module_json = #{moduleJson}</if>
		</where>
	</select>

	<select id="selectShipByShipId" parameterType="Long" resultMap="ShipResult">
		<include refid="selectShipVo"/>
		where ship_id = #{shipId}
	</select>

	<insert id="insertShip" parameterType="Ship" useGeneratedKeys="true" keyProperty="shipId">
		insert into bu_ship
		<trim prefix="(" suffix=")" suffixOverrides=",">
			<if test="daShipId != null">da_ship_id,</if>
			<if test="deptId != null">dept_id,</if>
			<if test="sn != null and sn != ''">sn,</if>
			<if test="name != null and name != ''">name,</if>
			<if test="mmsi != null">mmsi,</if>
			<if test="callSign != null">call_sign,</if>
			<if test="imo != null">imo,</if>
			<if test="status != null">status,</if>
			<if test="remark != null">remark,</if>
			<if test="createBy != null">create_by,</if>
			<if test="createTime != null">create_time,</if>
			<if test="updateBy != null">update_by,</if>
			<if test="updateTime != null">update_time,</if>
			<if test="moduleJson != null">module_json,</if>
		</trim>
		<trim prefix="values (" suffix=")" suffixOverrides=",">
			<if test="daShipId != null">#{daShipId},</if>
			<if test="deptId != null">#{deptId},</if>
			<if test="sn != null and sn != ''">#{sn},</if>
			<if test="name != null and name != ''">#{name},</if>
			<if test="mmsi != null">#{mmsi},</if>
			<if test="callSign != null">#{callSign},</if>
			<if test="imo != null">#{imo},</if>
			<if test="status != null">#{status},</if>
			<if test="remark != null">#{remark},</if>
			<if test="createBy != null">#{createBy},</if>
			<if test="createTime != null">#{createTime},</if>
			<if test="updateBy != null">#{updateBy},</if>
			<if test="updateTime != null">#{updateTime},</if>
			<if test="moduleJson != null">#{moduleJson},</if>
		</trim>
	</insert>

	<update id="updateShip" parameterType="Ship">
		update bu_ship
		<trim prefix="SET" suffixOverrides=",">
			<if test="daShipId != null">da_ship_id = #{daShipId},</if>
			<if test="deptId != null">dept_id = #{deptId},</if>
			<if test="sn != null and sn != ''">sn = #{sn},</if>
			<if test="name != null and name != ''">name = #{name},</if>
			<if test="mmsi != null">mmsi = #{mmsi},</if>
			<if test="callSign != null">call_sign = #{callSign},</if>
			<if test="imo != null">imo = #{imo},</if>
			<if test="status != null">status = #{status},</if>
			<if test="remark != null">remark = #{remark},</if>
			<if test="createBy != null">create_by = #{createBy},</if>
			<if test="createTime != null">create_time = #{createTime},</if>
			<if test="updateBy != null">update_by = #{updateBy},</if>
			<if test="updateTime != null">update_time = #{updateTime},</if>
			<if test="moduleJson != null">module_json = #{moduleJson},</if>
		</trim>
		where ship_id = #{shipId}
	</update>

	<delete id="deleteShipByShipId" parameterType="Long">
		delete from bu_ship where ship_id = #{shipId}
	</delete>

	<delete id="deleteShipByShipIds" parameterType="String">
		delete from bu_ship where ship_id in
		<foreach item="shipId" collection="array" open="(" separator="," close=")">
			#{shipId}
		</foreach>
	</delete>
</mapper>