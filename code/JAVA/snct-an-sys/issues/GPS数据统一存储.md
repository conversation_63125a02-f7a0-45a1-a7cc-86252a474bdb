# GPS数据统一存储任务

## 背景
之前北斗2和北斗3设备的GPS数据被存储在不同的HBase表中，表名是根据设备code动态生成的。这导致数据分散在多个表中，不利于统一查询和管理。

## 修改内容
1. 修改了`saveGpsVo`方法，将表名固定为"snct:gps"，不再使用动态生成的表名
2. 修改了`saveGpsData`方法，简化了对`saveGpsVo`的调用，不再传递tableSuffix参数
3. 修改了`initHBaseTables`方法，确保包含创建gps表的逻辑
4. 调整了Redis缓存key的生成方式，保持一致性

## 修改原因
- 由于rowkey中已经包含了设备ID等信息，可以唯一标识不同设备的数据
- 将所有GPS设备数据统一存储在一张表中，便于统一查询和管理
- 简化了代码逻辑，减少了不必要的复杂度

## 影响范围
- 只影响新数据的存储，历史数据仍然保留在原表中
- 查询时需要注意新旧数据的位置差异，可能需要从多个表中合并查询结果

## 后续工作
- 考虑将历史数据迁移到新表中
- 更新相关的查询逻辑，统一从新表中查询GPS数据 